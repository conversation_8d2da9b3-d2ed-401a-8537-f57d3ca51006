/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-client)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2F.react-email%2Fsrc%2Fstyles%2Fglobals.css&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2F.react-email%2Fsrc%2Fstyles%2Fglobals.css&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("(() => __webpack_require__.e(/*! import() */ \"_app-client_node_modules_next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arg-7ffe8b\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-client)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23)));\n(() => __webpack_require__.e(/*! import() */ \"_app-client_src_styles_globals_css\").then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/globals.css */ \"(app-client)/./src/styles/globals.css\")))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1jbGllbnQpLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lMkZVc2VycyUyRnlva2VzaGtzJTJGRG93bmxvYWRzJTJGaW5jcmVzY28lMkZmbGlua2slMkZyZWFjdC1lbWFpbC1zdGFydGVyJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRnlva2VzaGtzJTJGRG93bmxvYWRzJTJGaW5jcmVzY28lMkZmbGlua2slMkZyZWFjdC1lbWFpbC1zdGFydGVyJTJGLnJlYWN0LWVtYWlsJTJGc3JjJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3Mmc2VydmVyPWZhbHNlIS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxPQUFPLG9sQkFBb1M7QUFDM1MsT0FBTyx3TUFBcUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz80MDViIl0sInNvdXJjZXNDb250ZW50IjpbIigoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwibGF6eVwiICovIFwiL1VzZXJzL3lva2VzaGtzL0Rvd25sb2Fkcy9pbmNyZXNjby9mbGlua2svcmVhY3QtZW1haWwtc3RhcnRlci9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP3tcXFwicGF0aFxcXCI6XFxcInNyYy9hcHAvbGF5b3V0LnRzeFxcXCIsXFxcImltcG9ydFxcXCI6XFxcIkludGVyXFxcIixcXFwiYXJndW1lbnRzXFxcIjpbe1xcXCJzdWJzZXRzXFxcIjpbXFxcImxhdGluXFxcIl0sXFxcInZhcmlhYmxlXFxcIjpcXFwiLS1mb250LWludGVyXFxcIn1dLFxcXCJ2YXJpYWJsZU5hbWVcXFwiOlxcXCJpbnRlclxcXCJ9XCIpKTtcbigoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwibGF6eVwiICovIFwiL1VzZXJzL3lva2VzaGtzL0Rvd25sb2Fkcy9pbmNyZXNjby9mbGlua2svcmVhY3QtZW1haWwtc3RhcnRlci8ucmVhY3QtZW1haWwvc3JjL3N0eWxlcy9nbG9iYWxzLmNzc1wiKSkiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-client)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2F.react-email%2Fsrc%2Fstyles%2Fglobals.css&server=false!\n"));

/***/ })

});