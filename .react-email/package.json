{"name": "react-email-client", "version": "0.0.14", "description": "The React Email preview application", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@radix-ui/colors": "0.1.8", "@radix-ui/react-collapsible": "1.0.1", "@radix-ui/react-popover": "1.0.2", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-toggle-group": "1.0.1", "@radix-ui/react-tooltip": "1.0.2", "@react-email/render": "0.0.7", "classnames": "2.3.2", "framer-motion": "8.4.6", "next": "13.2.4", "prism-react-renderer": "1.3.5", "react": "18.2.0", "react-dom": "18.2.0", "@react-email/components": "0.0.6", "react-email": "1.9.3"}, "devDependencies": {"@types/classnames": "2.3.1", "@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "autoprefixer": "10.4.13", "eslint": "8.36.0", "eslint-config-next": "13.2.4", "eslint-config-prettier": "8.7.0", "eslint-plugin-simple-import-sort": "10.0.0", "eslint-plugin-unused-imports": "2.0.0", "postcss": "8.4.19", "prettier": "2.8.4", "tailwindcss": "3.2.4", "typescript": "4.9.3"}, "readme": "ERROR: No README data found!", "_id": "react-email-client@0.0.14"}