"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "../node_modules/@swc/helpers/lib/_extends.js":
/*!****************************************************!*\
  !*** ../node_modules/@swc/helpers/lib/_extends.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _extends;\n    }\n}));\nfunction extends_() {\n    extends_ = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return extends_.apply(this, arguments);\n}\nfunction _extends() {\n    return extends_.apply(this, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX2V4dGVuZHMuanMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRiwyQ0FBMEM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7QUFDRjtBQUNBO0FBQ0EsdUJBQXVCLHNCQUFzQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhY3QtZW1haWwtY2xpZW50Ly4uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvbGliL19leHRlbmRzLmpzP2E1ZmMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJkZWZhdWx0XCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfZXh0ZW5kcztcbiAgICB9XG59KTtcbmZ1bmN0aW9uIGV4dGVuZHNfKCkge1xuICAgIGV4dGVuZHNfID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbih0YXJnZXQpIHtcbiAgICAgICAgZm9yKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKyl7XG4gICAgICAgICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldO1xuICAgICAgICAgICAgZm9yKHZhciBrZXkgaW4gc291cmNlKXtcbiAgICAgICAgICAgICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHNvdXJjZSwga2V5KSkge1xuICAgICAgICAgICAgICAgICAgICB0YXJnZXRba2V5XSA9IHNvdXJjZVtrZXldO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGFyZ2V0O1xuICAgIH07XG4gICAgcmV0dXJuIGV4dGVuZHNfLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgICByZXR1cm4gZXh0ZW5kc18uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../node_modules/@swc/helpers/lib/_extends.js\n");

/***/ }),

/***/ "../node_modules/@swc/helpers/lib/_interop_require_default.js":
/*!********************************************************************!*\
  !*** ../node_modules/@swc/helpers/lib/_interop_require_default.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _interopRequireDefault;\n    }\n}));\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0LmpzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0YsMkNBQTBDO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWN0LWVtYWlsLWNsaWVudC8uLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuanM/MzIyYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcImRlZmF1bHRcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQ7XG4gICAgfVxufSk7XG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikge1xuICAgIHJldHVybiBvYmogJiYgb2JqLl9fZXNNb2R1bGUgPyBvYmogOiB7XG4gICAgICAgIGRlZmF1bHQ6IG9ialxuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../node_modules/@swc/helpers/lib/_interop_require_default.js\n");

/***/ }),

/***/ "../node_modules/@swc/helpers/lib/_interop_require_wildcard.js":
/*!*********************************************************************!*\
  !*** ../node_modules/@swc/helpers/lib/_interop_require_wildcard.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _interopRequireWildcard;\n    }\n}));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/@swc/helpers/lib/_interop_require_wildcard.js\n");

/***/ }),

/***/ "../node_modules/next/dist/pages/_error.js":
/*!*************************************************!*\
  !*** ../node_modules/next/dist/pages/_error.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"../node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"react\"));\nvar _head = _interop_require_default(__webpack_require__(/*! ../shared/lib/head */ \"../node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: \"Bad Request\",\n    404: \"This page could not be found\",\n    405: \"Method Not Allowed\",\n    500: \"Internal Server Error\"\n};\nfunction _getInitialProps({ res , err  }) {\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: \"100vh\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n    },\n    desc: {\n        display: \"inline-block\",\n        textAlign: \"left\"\n    },\n    h1: {\n        display: \"inline-block\",\n        margin: \"0 20px 0 0\",\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: \"top\",\n        lineHeight: \"49px\"\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: \"49px\",\n        margin: 0\n    }\n};\nvar _Component;\nclass Error extends (_Component = _react.default.Component) {\n    render() {\n        const { statusCode , withDarkMode =true  } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.error\n        }, /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement(\"title\", null, statusCode ? `${statusCode}: ${title}` : \"Application error: a client-side exception has occurred\")), /*#__PURE__*/ _react.default.createElement(\"div\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}${withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\"}`\n            }\n        }), statusCode ? /*#__PURE__*/ _react.default.createElement(\"h1\", {\n            className: \"next-error-h1\",\n            style: styles.h1\n        }, statusCode) : null, /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.desc\n        }, /*#__PURE__*/ _react.default.createElement(\"h2\", {\n            style: styles.h2\n        }, this.props.title || statusCode ? title : /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, \"Application error: a client-side exception has occurred (see the browser console for more information)\"), \".\"))));\n    }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nexports[\"default\"] = Error;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/pages/_error.js\n");

/***/ }),

/***/ "../node_modules/next/dist/shared/lib/head.js":
/*!****************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/head.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.defaultHead = defaultHead;\nexports[\"default\"] = void 0;\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"../node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"../node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"../node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _react = _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nvar _sideEffect = _interop_require_default(__webpack_require__(/*! ./side-effect */ \"./side-effect\"));\nvar _ampContext = __webpack_require__(/*! ./amp-context */ \"./amp-context\");\nvar _headManagerContext = __webpack_require__(/*! ./head-manager-context */ \"./head-manager-context\");\nvar _ampMode = __webpack_require__(/*! ./amp-mode */ \"./amp-mode\");\nvar _warnOnce = __webpack_require__(/*! ./utils/warn-once */ \"./utils/warn-once\");\nfunction defaultHead(inAmpMode = false) {\n    const head = [\n        /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            charSet: \"utf-8\"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === \"string\" || typeof child === \"number\") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    \"name\",\n    \"httpEquiv\",\n    \"charSet\",\n    \"itemProp\"\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case \"title\":\n            case \"base\":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case \"meta\":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === \"charSet\") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode  } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n                const srcMessage = c.props[\"src\"] ? `<script> tag with src=\"${c.props[\"src\"]}\"` : `inline <script>`;\n                (0, _warnOnce).warnOnce(`Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`);\n            } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n                (0, _warnOnce).warnOnce(`Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props[\"href\"]}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`);\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head({ children  }) {\n    const ampState = (0, _react).useContext(_ampContext.AmpStateContext);\n    const headManager = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n    return /*#__PURE__*/ _react.default.createElement(_sideEffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampMode).isInAmpMode(ampState)\n    }, children);\n}\nvar _default = Head;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/shared/lib/head.js\n");

/***/ }),

/***/ "react":
/*!*******************************************!*\
  !*** external "next/dist/compiled/react" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/react");

/***/ }),

/***/ "./amp-context":
/*!******************************************************!*\
  !*** external "next/dist/shared/lib/amp-context.js" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/amp-context.js");

/***/ }),

/***/ "./amp-mode":
/*!***************************************************!*\
  !*** external "next/dist/shared/lib/amp-mode.js" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/amp-mode.js");

/***/ }),

/***/ "./head-manager-context":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/head-manager-context.js" ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/head-manager-context.js");

/***/ }),

/***/ "./side-effect":
/*!******************************************************!*\
  !*** external "next/dist/shared/lib/side-effect.js" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/side-effect.js");

/***/ }),

/***/ "./utils/warn-once":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/utils/warn-once.js" ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = require("next/dist/shared/lib/utils/warn-once.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("../node_modules/next/dist/pages/_error.js"));
module.exports = __webpack_exports__;

})();