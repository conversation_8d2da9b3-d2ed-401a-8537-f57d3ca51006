{"c": ["app-client-internals", "app/layout", "app/preview/[slug]/page", "webpack", "_app-client_src_styles_globals_css"], "r": [], "m": ["(app-client)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&server=false!", "(app-client)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2F.react-email%2Fsrc%2Fstyles%2Fglobals.css&modules=%2FUsers%2Fyokeshks%2FDownloads%2Fincresco%2Fflinkk%2Freact-email-starter%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=false!"]}