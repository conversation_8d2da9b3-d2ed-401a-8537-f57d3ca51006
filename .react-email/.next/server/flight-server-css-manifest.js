self.__RSC_CSS_MANIFEST={
  "__entry_css_mods__": {
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/page": [
      "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/styles/globals.css",
      "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}"
    ]
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/page.tsx": [
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}",
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/styles/globals.css"
  ],
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/layout.tsx": [
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}",
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/styles/globals.css"
  ]
}