"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: logoContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                    src: \"https://www.powderbyrne.com/wp-content/themes/monster-powder-byrne-theme/theme/images/stories-logo.png\",\n                                    width: \"120\",\n                                    alt: \"PowderByrne\",\n                                    style: logo\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: buttonContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    href: resetLink,\n                                    style: button,\n                                    children: \"Access PB40 Ops\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Need assistance? Contact our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f8fafc\",\n    fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif\",\n    color: \"#1e293b\",\n    margin: \"0\",\n    padding: \"20px 10px\",\n    lineHeight: \"1.6\",\n    width: \"100%\"\n};\nconst container = {\n    maxWidth: \"580px\",\n    width: \"100%\",\n    margin: \"0 auto\",\n    padding: \"30px 20px\",\n    backgroundColor: \"#ffffff\",\n    borderRadius: \"16px\",\n    boxShadow: \"0 4px 6px -1px rgba(38, 93, 166, 0.1), 0 2px 4px -1px rgba(38, 93, 166, 0.06)\",\n    border: \"1px solid #e2e8f0\",\n    boxSizing: \"border-box\"\n};\nconst box = {\n    padding: \"0\",\n    width: \"100%\",\n    maxWidth: \"580px\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n};\nconst logo = {\n    maxWidth: \"100px\",\n    display: \"block\",\n    margin: \"0 auto\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#1e3a5f\",\n    marginBottom: \"32px\",\n    fontSize: \"32px\",\n    fontWeight: \"800\",\n    letterSpacing: \"-0.025em\",\n    lineHeight: \"1.2\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#475569\",\n    fontSize: \"18px\",\n    lineHeight: \"1.7\",\n    margin: \"24px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#0f172a\",\n    marginBottom: \"24px\",\n    fontSize: \"24px\",\n    textAlign: \"left\",\n    fontWeight: \"700\",\n    letterSpacing: \"-0.025em\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f8fafc, #f1f5f9)\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"32px\",\n    borderRadius: \"16px\",\n    marginTop: \"32px\",\n    textAlign: \"left\",\n    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n};\nconst credentialItem = {\n    margin: \"16px 0\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"500\"\n};\nconst passwordCode = {\n    backgroundColor: \"#0f172a\",\n    color: \"#ffffff\",\n    padding: \"12px 20px\",\n    borderRadius: \"8px\",\n    fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace\",\n    fontWeight: \"600\",\n    fontSize: \"16px\",\n    display: \"inline-block\",\n    marginLeft: \"12px\",\n    letterSpacing: \"0.5px\",\n    border: \"none\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f8fafc\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"24px\",\n    borderRadius: \"12px\",\n    margin: \"32px 0\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#0f172a\",\n    textAlign: \"left\",\n    fontSize: \"18px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst buttonContainer = {\n    textAlign: \"center\",\n    margin: \"30px 0\",\n    padding: \"0 10px\",\n    width: \"100%\"\n};\nconst button = {\n    display: \"inline-block\",\n    paddingTop: \"16px\",\n    paddingBottom: \"16px\",\n    paddingLeft: \"32px\",\n    paddingRight: \"32px\",\n    backgroundColor: \"#265DA6\",\n    color: \"#ffffff\",\n    textDecoration: \"none\",\n    fontWeight: \"700\",\n    borderRadius: \"12px\",\n    fontSize: \"16px\",\n    textAlign: \"center\",\n    border: \"none\",\n    fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    lineHeight: \"1.2\",\n    minWidth: \"200px\",\n    maxWidth: \"90%\",\n    boxShadow: \"0 4px 14px 0 rgba(38, 93, 166, 0.39)\",\n    letterSpacing: \"0.025em\",\n    msoLineHeightRule: \"exactly\",\n    msoBorderAlt: \"none\",\n    msoHide: \"none\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fef3c7\",\n    border: \"2px solid #fbbf24\",\n    color: \"#92400e\",\n    padding: \"24px\",\n    margin: \"32px 0\",\n    borderRadius: \"12px\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#92400e\",\n    textAlign: \"left\",\n    fontSize: \"16px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst welcomeMessage = {\n    marginTop: \"48px\",\n    textAlign: \"center\",\n    fontWeight: \"600\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    lineHeight: \"1.6\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"2px solid #f1f5f9\",\n    margin: \"48px 0\"\n};\nconst anchor = {\n    color: \"#3b82f6\",\n    textDecoration: \"none\",\n    fontWeight: \"600\"\n};\nconst footer = {\n    fontSize: \"14px\",\n    lineHeight: \"1.6\",\n    color: \"#64748b\",\n    textAlign: \"center\",\n    marginTop: \"40px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;