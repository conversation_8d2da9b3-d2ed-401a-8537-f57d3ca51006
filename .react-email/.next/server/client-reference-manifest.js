self.__RSC_MANIFEST={
  "__ssr_module_mapping__": {
    "(app-client)/../node_modules/next/dist/client/components/app-router.js": {
      "*": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/app-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "*",
        "async": false
      },
      "": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/app-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "",
        "async": false
      },
      "default": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/app-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "default",
        "async": false
      }
    },
    "(app-client)/../node_modules/next/dist/client/components/error-boundary.js": {
      "*": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/error-boundary.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "*",
        "async": false
      },
      "": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/error-boundary.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "",
        "async": false
      },
      "default": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/error-boundary.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "default",
        "async": false
      }
    },
    "(app-client)/../node_modules/next/dist/client/components/layout-router.js": {
      "*": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/layout-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "*",
        "async": false
      },
      "": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/layout-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "",
        "async": false
      },
      "default": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/layout-router.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "default",
        "async": false
      }
    },
    "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js": {
      "*": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/render-from-template-context.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "*",
        "async": false
      },
      "": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/render-from-template-context.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "",
        "async": false
      },
      "default": {
        "id": "(sc_client)/../node_modules/next/dist/client/components/render-from-template-context.js",
        "chunks": [
          "app-client-internals:app-client-internals"
        ],
        "name": "default",
        "async": false
      }
    },
    "(app-client)/./src/app/preview/[slug]/preview.tsx": {
      "*": {
        "id": "(sc_client)/./src/app/preview/[slug]/preview.tsx",
        "chunks": [
          "app/preview/[slug]/page:app/preview/[slug]/page"
        ],
        "name": "*",
        "async": false
      },
      "": {
        "id": "(sc_client)/./src/app/preview/[slug]/preview.tsx",
        "chunks": [
          "app/preview/[slug]/page:app/preview/[slug]/page"
        ],
        "name": "",
        "async": false
      },
      "default": {
        "id": "(sc_client)/./src/app/preview/[slug]/preview.tsx",
        "chunks": [
          "app/preview/[slug]/page:app/preview/[slug]/page"
        ],
        "name": "default",
        "async": false
      }
    }
  },
  "__edge_ssr_module_mapping__": {},
  "__entry_css_files__": {
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/page": [
      "static/css/app/preview/[slug]/page.css"
    ],
    "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/layout": [
      "static/css/_app-client_src_styles_globals_css.css",
      "static/css/_app-client_node_modules_next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arg-7ffe8b.css"
    ]
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/app-router.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/app-router.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/app-router.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/app-router.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/app-router.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/app-router.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/app-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/error-boundary.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/error-boundary.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/error-boundary.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/error-boundary.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/error-boundary.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/error-boundary.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/error-boundary.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/layout-router.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/layout-router.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/layout-router.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/layout-router.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/layout-router.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/layout-router.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/layout-router.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/render-from-template-context.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/render-from-template-context.js": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/render-from-template-context.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/render-from-template-context.js#": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/client/components/render-from-template-context.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/dist/esm/client/components/render-from-template-context.js#default": {
    "id": "(app-client)/../node_modules/next/dist/client/components/render-from-template-context.js",
    "chunks": [
      "app-client-internals:app-client-internals"
    ],
    "name": "default",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/styles/globals.css#": {
    "id": "null",
    "name": "",
    "chunks": [
      "static/css/app/preview/[slug]/page.css",
      "static/css/_app-client_src_styles_globals_css.css"
    ]
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}#": {
    "id": "null",
    "name": "",
    "chunks": [
      "static/css/app/preview/[slug]/page.css",
      "static/css/_app-client_node_modules_next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arg-7ffe8b.css"
    ]
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/preview.tsx": {
    "id": "(app-client)/./src/app/preview/[slug]/preview.tsx",
    "chunks": [
      "app/preview/[slug]/page:app/preview/[slug]/page"
    ],
    "name": "*",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/preview.tsx#": {
    "id": "(app-client)/./src/app/preview/[slug]/preview.tsx",
    "chunks": [
      "app/preview/[slug]/page:app/preview/[slug]/page"
    ],
    "name": "",
    "async": false
  },
  "/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/.react-email/src/app/preview/[slug]/preview.tsx#default": {
    "id": "(app-client)/./src/app/preview/[slug]/preview.tsx",
    "chunks": [
      "app/preview/[slug]/page:app/preview/[slug]/page"
    ],
    "name": "default",
    "async": false
  }
}