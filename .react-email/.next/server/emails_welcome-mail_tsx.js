"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\n        :root {\n          --bg-color: #f9fafb;\n          --container-bg: #ffffff;\n          --text-primary: #1f2937;\n          --text-secondary: #4b5563;\n          --text-muted: #6b7280;\n          --border-color: #e5e7eb;\n          --border-light: #f3f4f6;\n          --shadow-color: rgba(0, 0, 0, 0.05);\n          --credentials-bg: #f1f5f9;\n          --warning-bg: #fff7e6;\n          --warning-border: #ffedc2;\n          --warning-text: #8a6d3b;\n          --primary-color: #2563eb;\n          --primary-hover: #1e40af;\n          --password-bg: #1e293b;\n          --password-text: #ffffff;\n        }\n\n        @media (prefers-color-scheme: dark) {\n          :root {\n            --bg-color: #111827;\n            --container-bg: #1f2937;\n            --text-primary: #f9fafb;\n            --text-secondary: #d1d5db;\n            --text-muted: #9ca3af;\n            --border-color: #374151;\n            --border-light: #4b5563;\n            --shadow-color: rgba(0, 0, 0, 0.3);\n            --credentials-bg: #2d3748;\n            --warning-bg: #4b3f28;\n            --warning-border: #6b5b2a;\n            --warning-text: #fcd34d;\n            --primary-color: #3b82f6;\n            --primary-hover: #2563eb;\n            --password-bg: #f9fafb;\n            --password-text: #1e293b;\n          }\n        }\n      `\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 76,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: logoContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                    src: \"https://dev-assets.store.flinkk.io/powderbyrne/logo.png\",\n                                    width: \"100\",\n                                    alt: \"Brand Logo\",\n                                    style: logo\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 44\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 44\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                href: resetLink,\n                                style: button,\n                                children: \"Access PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    \"Need assistance? Contact our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 42\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 77,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f9fafb\",\n    fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n    color: \"#1f2937\",\n    margin: \"0\",\n    padding: \"0\",\n    lineHeight: \"1.6\"\n};\nconst container = {\n    maxWidth: \"650px\",\n    margin: \"40px auto\",\n    padding: \"32px\",\n    backgroundColor: \"#ffffff\",\n    border: \"1px solid #e5e7eb\",\n    borderRadius: \"12px\",\n    boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.05)\"\n};\nconst box = {\n    padding: \"0\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\"\n};\nconst logo = {\n    maxWidth: \"100px\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#1f2937\",\n    marginBottom: \"20px\",\n    fontSize: \"24px\",\n    fontWeight: \"bold\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#4b5563\",\n    fontSize: \"16px\",\n    lineHeight: \"24px\",\n    margin: \"16px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#1f2937\",\n    marginBottom: \"15px\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"bold\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f1f5f9, #e2e8f0)\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"16px\",\n    borderRadius: \"6px\",\n    marginTop: \"20px\",\n    textAlign: \"left\"\n};\nconst credentialItem = {\n    margin: \"8px 0\",\n    color: \"#1f2937\",\n    fontSize: \"16px\",\n    textAlign: \"left\"\n};\nconst passwordCode = {\n    backgroundColor: \"#1e293b\",\n    color: \"#ffffff\",\n    padding: \"6px 12px\",\n    borderRadius: \"6px\",\n    fontFamily: \"monospace\",\n    fontWeight: \"bold\",\n    fontSize: \"14px\",\n    display: \"inline-block\",\n    marginLeft: \"10px\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f1f5f9\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"15px\",\n    borderRadius: \"6px\",\n    margin: \"20px 0\",\n    textAlign: \"left\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#1f2937\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst button = {\n    display: \"block\",\n    width: \"max-content\",\n    margin: \"20px auto\",\n    padding: \"12px 28px\",\n    backgroundColor: \"#2563eb\",\n    color: \"white\",\n    textDecoration: \"none\",\n    fontWeight: \"bold\",\n    borderRadius: \"8px\",\n    fontSize: \"16px\",\n    textAlign: \"center\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fff7e6\",\n    border: \"1px solid #ffedc2\",\n    color: \"#8a6d3b\",\n    padding: \"15px\",\n    margin: \"20px 0\",\n    borderRadius: \"6px\",\n    textAlign: \"left\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#8a6d3b\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst welcomeMessage = {\n    marginTop: \"30px\",\n    textAlign: \"center\",\n    fontWeight: \"500\",\n    color: \"#1f2937\",\n    fontSize: \"16px\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"1px solid #f3f4f6\",\n    margin: \"40px 0\"\n};\nconst anchor = {\n    color: \"#2563eb\",\n    textDecoration: \"none\"\n};\nconst footer = {\n    fontSize: \"13px\",\n    lineHeight: \"1.4\",\n    color: \"#6b7280\",\n    textAlign: \"center\",\n    marginTop: \"30px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;