"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("amp",{

/***/ "../node_modules/next/dist/shared/lib/router/router.js":
/*!*************************************************************!*\
  !*** ../node_modules/next/dist/shared/lib/router/router.js ***!
  \*************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.matchesMiddleware = matchesMiddleware;\nexports.createKey = createKey;\nexports[\"default\"] = void 0;\nvar _async_to_generator = (__webpack_require__(/*! @swc/helpers/lib/_async_to_generator.js */ \"../node_modules/@swc/helpers/lib/_async_to_generator.js\")[\"default\"]);\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"../node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"../node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"../node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _removeTrailingSlash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"../node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nvar _routeLoader = __webpack_require__(/*! ../../../client/route-loader */ \"../node_modules/next/dist/client/route-loader.js\");\nvar _script = __webpack_require__(/*! ../../../client/script */ \"../node_modules/next/dist/client/script.js\");\nvar _isError = _interop_require_wildcard(__webpack_require__(/*! ../../../lib/is-error */ \"../node_modules/next/dist/lib/is-error.js\"));\nvar _denormalizePagePath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"../node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nvar _normalizeLocalePath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"../node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nvar _mitt = _interop_require_default(__webpack_require__(/*! ../mitt */ \"../node_modules/next/dist/shared/lib/mitt.js\"));\nvar _utils = __webpack_require__(/*! ../utils */ \"../node_modules/next/dist/shared/lib/utils.js\");\nvar _isDynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"../node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nvar _parseRelativeUrl = __webpack_require__(/*! ./utils/parse-relative-url */ \"../node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nvar _resolveRewrites = _interop_require_default(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?36cb\"));\nvar _routeMatcher = __webpack_require__(/*! ./utils/route-matcher */ \"../node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nvar _routeRegex = __webpack_require__(/*! ./utils/route-regex */ \"../node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nvar _formatUrl = __webpack_require__(/*! ./utils/format-url */ \"../node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nvar _detectDomainLocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"../node_modules/next/dist/client/detect-domain-locale.js\");\nvar _parsePath = __webpack_require__(/*! ./utils/parse-path */ \"../node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nvar _addLocale = __webpack_require__(/*! ../../../client/add-locale */ \"../node_modules/next/dist/client/add-locale.js\");\nvar _removeLocale = __webpack_require__(/*! ../../../client/remove-locale */ \"../node_modules/next/dist/client/remove-locale.js\");\nvar _removeBasePath = __webpack_require__(/*! ../../../client/remove-base-path */ \"../node_modules/next/dist/client/remove-base-path.js\");\nvar _addBasePath = __webpack_require__(/*! ../../../client/add-base-path */ \"../node_modules/next/dist/client/add-base-path.js\");\nvar _hasBasePath = __webpack_require__(/*! ../../../client/has-base-path */ \"../node_modules/next/dist/client/has-base-path.js\");\nvar _isApiRoute = __webpack_require__(/*! ../../../lib/is-api-route */ \"../node_modules/next/dist/lib/is-api-route.js\");\nvar _getNextPathnameInfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"../node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nvar _formatNextPathnameInfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"../node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nvar _compareStates = __webpack_require__(/*! ./utils/compare-states */ \"../node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nvar _isLocalUrl = __webpack_require__(/*! ./utils/is-local-url */ \"../node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nvar _isBot = __webpack_require__(/*! ./utils/is-bot */ \"../node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nvar _omit = __webpack_require__(/*! ./utils/omit */ \"../node_modules/next/dist/shared/lib/router/utils/omit.js\");\nvar _resolveHref = __webpack_require__(/*! ./utils/resolve-href */ \"../node_modules/next/dist/shared/lib/router/utils/resolve-href.js\");\nvar _interpolateAs = __webpack_require__(/*! ./utils/interpolate-as */ \"../node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nvar _handleSmoothScroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"../node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nfunction matchesMiddleware(options) {\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction _matchesMiddleware() {\n    _matchesMiddleware = _async_to_generator(function*(options) {\n        const matchers = yield Promise.resolve(options.router.pageLoader.getMiddleware());\n        if (!matchers) return false;\n        const { pathname: asPathname  } = (0, _parsePath).parsePath(options.asPath);\n        // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n        const cleanedAs = (0, _hasBasePath).hasBasePath(asPathname) ? (0, _removeBasePath).removeBasePath(asPathname) : asPathname;\n        const asWithBasePathAndLocale = (0, _addBasePath).addBasePath((0, _addLocale).addLocale(cleanedAs, options.locale));\n        // Check only path match on client. Matching \"has\" should be done on server\n        // where we can access more info such as headers, HttpOnly cookie, etc.\n        return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n    });\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils).getLocationOrigin();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolveHref).resolveHref(router, url, true);\n    const origin = (0, _utils).getLocationOrigin();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addBasePath).addBasePath(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolveHref).resolveHref(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addBasePath).addBasePath(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removeTrailingSlash).removeTrailingSlash((0, _denormalizePagePath).denormalizePagePath(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isDynamic).isDynamicRoute(page) && (0, _routeRegex).getRouteRegex(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\") || undefined) {\n            const parsedRewriteTarget = (0, _parseRelativeUrl).parseRelativeUrl(rewriteTarget);\n            const pathnameInfo = (0, _getNextPathnameInfo).getNextPathnameInfo(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removeTrailingSlash).removeTrailingSlash(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeLoader).getClientBuildManifest()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites  }] = param;\n                let as = (0, _addLocale).addLocale(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isDynamic).isDynamicRoute(as) || !rewriteHeader && pages.includes((0, _normalizeLocalePath).normalizeLocalePath((0, _removeBasePath).removeBasePath(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getNextPathnameInfo).getNextPathnameInfo((0, _parseRelativeUrl).parseRelativeUrl(source).pathname, {\n                        parseData: true\n                    });\n                    as = (0, _addBasePath).addBasePath(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizeLocalePath).normalizeLocalePath((0, _removeBasePath).removeBasePath(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isDynamic).isDynamicRoute(resolvedHref)) {\n                    const matches = (0, _routeMatcher).getRouteMatcher((0, _routeRegex).getRouteRegex(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsePath).parsePath(source);\n        const pathname = (0, _formatNextPathnameInfo).formatNextPathnameInfo(_extends({}, (0, _getNextPathnameInfo).getNextPathnameInfo(src.pathname, {\n            nextConfig,\n            parseData: true\n        }), {\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        }));\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: \"\".concat(pathname).concat(src.query).concat(src.hash)\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src = (0, _parsePath).parsePath(redirectTarget);\n            const pathname = (0, _formatNextPathnameInfo).formatNextPathnameInfo(_extends({}, (0, _getNextPathnameInfo).getNextPathnameInfo(src.pathname, {\n                nextConfig,\n                parseData: true\n            }), {\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            }));\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: \"\".concat(pathname).concat(src.query).concat(src.hash),\n                newUrl: \"\".concat(pathname).concat(src.query).concat(src.hash)\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nfunction withMiddlewareEffects(options) {\n    return _withMiddlewareEffects.apply(this, arguments);\n}\nfunction _withMiddlewareEffects() {\n    _withMiddlewareEffects = _async_to_generator(function*(options) {\n        const matches = yield matchesMiddleware(options);\n        if (!matches || !options.fetchData) {\n            return null;\n        }\n        try {\n            const data = yield options.fetchData();\n            const effect = yield getMiddlewareData(data.dataHref, data.response, options);\n            return {\n                dataHref: data.dataHref,\n                json: data.json,\n                response: data.response,\n                text: data.text,\n                cacheKey: data.cacheKey,\n                effect\n            };\n        } catch (e) {\n            /**\n     * TODO: Revisit this in the future.\n     * For now we will not consider middleware data errors to be fatal.\n     * maybe we should revisit in the future.\n     */ return null;\n        }\n    });\n    return _withMiddlewareEffects.apply(this, arguments);\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref , inflightCache , isPrefetch , hasMiddleware , isServerRender , parseJSON , persistCache , isBackground , unstable_skipClientCache  } = param;\n    const { href: cacheKey  } = new URL(dataHref, window.location.href);\n    var ref1;\n    const getData = (params)=>{\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {}, isPrefetch && hasMiddleware ? {\n                \"x-middleware-prefetch\": \"1\"\n            } : {}),\n            method: (ref1 = params == null ? void 0 : params.method) != null ? ref1 : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var ref;\n                        if ((ref = tryToParseAsJSON(text)) == null ? void 0 : ref.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(\"Failed to load static props\");\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeLoader).markAssetError(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === \"Failed to fetch\" || // firefox\n            err.message === \"NetworkError when attempting to fetch resource.\" || // safari\n            err.message === \"Load failed\") {\n                (0, _routeLoader).markAssetError(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url , router  } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addBasePath).addBasePath((0, _addLocale).addLocale(router.asPath, router.locale))) {\n        throw new Error(\"Invariant: attempted to hard navigate to the same URL \".concat(url, \" \").concat(location.href));\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route , router  } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error('Abort fetching component for route: \"'.concat(route, '\"'));\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        if (false) {}\n        ({ url , as  } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        ({ url , as  } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    _bfl(as, resolvedAs, locale) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            if (true) {\n                let matchesBflStatic = false;\n                let matchesBflDynamic = false;\n                for (const curAs of [\n                    as,\n                    resolvedAs\n                ]){\n                    if (curAs) {\n                        const asNoSlash = (0, _removeTrailingSlash).removeTrailingSlash(new URL(curAs, \"http://n\").pathname);\n                        const asNoSlashLocale = (0, _addBasePath).addBasePath((0, _addLocale).addLocale(asNoSlash, locale || _this.locale));\n                        if (asNoSlash !== (0, _removeTrailingSlash).removeTrailingSlash(new URL(_this.asPath, \"http://n\").pathname)) {\n                            var ref, ref2;\n                            matchesBflStatic = matchesBflStatic || !!((ref = _this._bfl_s) == null ? void 0 : ref.has(asNoSlash)) || !!((ref2 = _this._bfl_s) == null ? void 0 : ref2.has(asNoSlashLocale));\n                            for (const normalizedAS of [\n                                asNoSlash,\n                                asNoSlashLocale\n                            ]){\n                                // if any sub-path of as matches a dynamic filter path\n                                // it should be hard navigated\n                                const curAsParts = normalizedAS.split(\"/\");\n                                for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                    var ref3;\n                                    const currentPart = curAsParts.slice(0, i).join(\"/\");\n                                    if (currentPart && ((ref3 = _this._bfl_d) == null ? void 0 : ref3.has(currentPart))) {\n                                        matchesBflDynamic = true;\n                                        break;\n                                    }\n                                }\n                            }\n                            // if the client router filter is matched then we trigger\n                            // a hard navigation\n                            if (matchesBflStatic || matchesBflDynamic) {\n                                handleHardNavigation({\n                                    url: (0, _addBasePath).addBasePath((0, _addLocale).addLocale(as, locale || _this.locale)),\n                                    router: _this\n                                });\n                                return new Promise(()=>{});\n                            }\n                        }\n                    }\n                }\n            }\n        })();\n    }\n    change(method, url, as, options, forcedScroll) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            if (!(0, _isLocalUrl).isLocalURL(url)) {\n                handleHardNavigation({\n                    url,\n                    router: _this\n                });\n                return false;\n            }\n            // WARNING: `_h` is an internal option for handing Next.js client-side\n            // hydration. Your app should _never_ use this property. It may change at\n            // any time without notice.\n            const isQueryUpdating = options._h === 1;\n            if (!isQueryUpdating && !options.shallow) {\n                yield _this._bfl(as, undefined, options.locale);\n            }\n            let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsePath).parsePath(url).pathname === (0, _parsePath).parsePath(as).pathname;\n            const nextState = _extends({}, _this.state);\n            // for static pages with query params in the URL we delay\n            // marking the router ready until after the query is updated\n            // or a navigation has occurred\n            const readyStateChange = _this.isReady !== true;\n            _this.isReady = true;\n            const isSsr = _this.isSsr;\n            if (!isQueryUpdating) {\n                _this.isSsr = false;\n            }\n            // if a route transition is already in progress before\n            // the query updating is triggered ignore query updating\n            if (isQueryUpdating && _this.clc) {\n                return false;\n            }\n            const prevLocale = nextState.locale;\n            if (false) { var ref; }\n            // marking route changes as a navigation start entry\n            if (_utils.ST) {\n                performance.mark(\"routeChange\");\n            }\n            const { shallow =false , scroll =true  } = options;\n            const routeProps = {\n                shallow\n            };\n            if (_this._inFlightRoute && _this.clc) {\n                if (!isSsr) {\n                    Router.events.emit(\"routeChangeError\", buildCancellationError(), _this._inFlightRoute, routeProps);\n                }\n                _this.clc();\n                _this.clc = null;\n            }\n            as = (0, _addBasePath).addBasePath((0, _addLocale).addLocale((0, _hasBasePath).hasBasePath(as) ? (0, _removeBasePath).removeBasePath(as) : as, options.locale, _this.defaultLocale));\n            const cleanedAs = (0, _removeLocale).removeLocale((0, _hasBasePath).hasBasePath(as) ? (0, _removeBasePath).removeBasePath(as) : as, nextState.locale);\n            _this._inFlightRoute = as;\n            const localeChange = prevLocale !== nextState.locale;\n            // If the url change is only related to a hash change\n            // We should not proceed. We should only change the state.\n            if (!isQueryUpdating && _this.onlyAHashChange(cleanedAs) && !localeChange) {\n                nextState.asPath = cleanedAs;\n                Router.events.emit(\"hashChangeStart\", as, routeProps);\n                // TODO: do we need the resolved href when only a hash change?\n                _this.changeState(method, url, as, _extends({}, options, {\n                    scroll: false\n                }));\n                if (scroll) {\n                    _this.scrollToHash(cleanedAs);\n                }\n                try {\n                    yield _this.set(nextState, _this.components[nextState.route], null);\n                } catch (err) {\n                    if ((0, _isError).default(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                Router.events.emit(\"hashChangeComplete\", as, routeProps);\n                return true;\n            }\n            let parsed = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            let { pathname , query  } = parsed;\n            // The build manifest needs to be loaded before auto-static dynamic pages\n            // get their query parameters to allow ensuring they can be parsed properly\n            // when rewritten to\n            let pages, rewrites;\n            try {\n                [pages, { __rewrites: rewrites  }] = yield Promise.all([\n                    _this.pageLoader.getPageList(),\n                    (0, _routeLoader).getClientBuildManifest(),\n                    _this.pageLoader.getMiddleware()\n                ]);\n            } catch (err) {\n                // If we fail to resolve the page list or client-build manifest, we must\n                // do a server-side transition:\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                return false;\n            }\n            // If asked to change the current URL we should reload the current page\n            // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n            // We also need to set the method = replaceState always\n            // as this should not go into the history (That's how browsers work)\n            // We should compare the new asPath to the current asPath, not the url\n            if (!_this.urlIsNew(cleanedAs) && !localeChange) {\n                method = \"replaceState\";\n            }\n            // we need to resolve the as value using rewrites for dynamic SSG\n            // pages to allow building the data URL correctly\n            let resolvedAs = as;\n            // url and as should always be prefixed with basePath by this\n            // point by either next/link or router.push/replace so strip the\n            // basePath from the pathname to match the pages dir 1-to-1\n            pathname = pathname ? (0, _removeTrailingSlash).removeTrailingSlash((0, _removeBasePath).removeBasePath(pathname)) : pathname;\n            let route = (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n            const parsedAsPathname = as.startsWith(\"/\") && (0, _parseRelativeUrl).parseRelativeUrl(as).pathname;\n            const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isDynamic).isDynamicRoute(route) || !(0, _routeMatcher).getRouteMatcher((0, _routeRegex).getRouteRegex(route))(parsedAsPathname)));\n            // we don't attempt resolve asPath when we need to execute\n            // middleware as the resolving will occur server-side\n            const isMiddlewareMatch = !options.shallow && (yield matchesMiddleware({\n                asPath: as,\n                locale: nextState.locale,\n                router: _this\n            }));\n            if (isQueryUpdating && isMiddlewareMatch) {\n                shouldResolveHref = false;\n            }\n            if (shouldResolveHref && pathname !== \"/_error\") {\n                options._shouldResolveHref = true;\n                if (false) {} else {\n                    parsed.pathname = resolveDynamicRoute(pathname, pages);\n                    if (parsed.pathname !== pathname) {\n                        pathname = parsed.pathname;\n                        parsed.pathname = (0, _addBasePath).addBasePath(pathname);\n                        if (!isMiddlewareMatch) {\n                            url = (0, _formatUrl).formatWithValidation(parsed);\n                        }\n                    }\n                }\n            }\n            if (!(0, _isLocalUrl).isLocalURL(as)) {\n                if (true) {\n                    throw new Error('Invalid href: \"'.concat(url, '\" and as: \"').concat(as, '\", received relative href and external as') + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\");\n                }\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                return false;\n            }\n            resolvedAs = (0, _removeLocale).removeLocale((0, _removeBasePath).removeBasePath(resolvedAs), nextState.locale);\n            route = (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n            let routeMatch = false;\n            if ((0, _isDynamic).isDynamicRoute(route)) {\n                const parsedAs = (0, _parseRelativeUrl).parseRelativeUrl(resolvedAs);\n                const asPathname = parsedAs.pathname;\n                const routeRegex = (0, _routeRegex).getRouteRegex(route);\n                routeMatch = (0, _routeMatcher).getRouteMatcher(routeRegex)(asPathname);\n                const shouldInterpolate = route === asPathname;\n                const interpolatedAs = shouldInterpolate ? (0, _interpolateAs).interpolateAs(route, asPathname, query) : {};\n                if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                    const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                    if (missingParams.length > 0 && !isMiddlewareMatch) {\n                        if (true) {\n                            console.warn(\"\".concat(shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\", \" failed to manually provide \") + \"the params: \".concat(missingParams.join(\", \"), \" in the `href`'s `query`\"));\n                        }\n                        throw new Error((shouldInterpolate ? \"The provided `href` (\".concat(url, \") value is missing query values (\").concat(missingParams.join(\", \"), \") to be interpolated properly. \") : \"The provided `as` value (\".concat(asPathname, \") is incompatible with the `href` value (\").concat(route, \"). \")) + \"Read more: https://nextjs.org/docs/messages/\".concat(shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\"));\n                    }\n                } else if (shouldInterpolate) {\n                    as = (0, _formatUrl).formatWithValidation(Object.assign({}, parsedAs, {\n                        pathname: interpolatedAs.result,\n                        query: (0, _omit).omit(query, interpolatedAs.params)\n                    }));\n                } else {\n                    // Merge params into `query`, overwriting any specified in search\n                    Object.assign(query, routeMatch);\n                }\n            }\n            if (!isQueryUpdating) {\n                Router.events.emit(\"routeChangeStart\", as, routeProps);\n            }\n            const isErrorRoute = _this.pathname === \"/404\" || _this.pathname === \"/_error\";\n            try {\n                var ref4, ref5, ref6;\n                let routeInfo = yield _this.getRouteInfo({\n                    route,\n                    pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps,\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    hasMiddleware: isMiddlewareMatch,\n                    unstable_skipClientCache: options.unstable_skipClientCache,\n                    isQueryUpdating: isQueryUpdating && !_this.isFallback,\n                    isMiddlewareRewrite\n                });\n                if (!isQueryUpdating && !options.shallow) {\n                    yield _this._bfl(as, \"resolvedAs\" in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n                }\n                if (\"route\" in routeInfo && isMiddlewareMatch) {\n                    pathname = routeInfo.route || route;\n                    route = pathname;\n                    if (!routeProps.shallow) {\n                        query = Object.assign({}, routeInfo.query || {}, query);\n                    }\n                    const cleanedParsedPathname = (0, _hasBasePath).hasBasePath(parsed.pathname) ? (0, _removeBasePath).removeBasePath(parsed.pathname) : parsed.pathname;\n                    if (routeMatch && pathname !== cleanedParsedPathname) {\n                        Object.keys(routeMatch).forEach((key)=>{\n                            if (routeMatch && query[key] === routeMatch[key]) {\n                                delete query[key];\n                            }\n                        });\n                    }\n                    if ((0, _isDynamic).isDynamicRoute(pathname)) {\n                        const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addBasePath).addBasePath((0, _addLocale).addLocale(new URL(as, location.href).pathname, nextState.locale), true);\n                        let rewriteAs = prefixedAs;\n                        if ((0, _hasBasePath).hasBasePath(rewriteAs)) {\n                            rewriteAs = (0, _removeBasePath).removeBasePath(rewriteAs);\n                        }\n                        if (false) {}\n                        const routeRegex = (0, _routeRegex).getRouteRegex(pathname);\n                        const curRouteMatch = (0, _routeMatcher).getRouteMatcher(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                        if (curRouteMatch) {\n                            Object.assign(query, curRouteMatch);\n                        }\n                    }\n                }\n                // If the routeInfo brings a redirect we simply apply it.\n                if (\"type\" in routeInfo) {\n                    if (routeInfo.type === \"redirect-internal\") {\n                        return _this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                    } else {\n                        handleHardNavigation({\n                            url: routeInfo.destination,\n                            router: _this\n                        });\n                        return new Promise(()=>{});\n                    }\n                }\n                const component = routeInfo.Component;\n                if (component && component.unstable_scriptLoader) {\n                    const scripts = [].concat(component.unstable_scriptLoader());\n                    scripts.forEach((script)=>{\n                        (0, _script).handleClientScriptLoad(script.props);\n                    });\n                }\n                // handle redirect on client-transition\n                if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                    if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                        // Use the destination from redirect without adding locale\n                        options.locale = false;\n                        const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                        // check if destination is internal (resolves to a page) and attempt\n                        // client-navigation if it is falling back to hard navigation if\n                        // it's not\n                        if (destination.startsWith(\"/\") && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                            const parsedHref = (0, _parseRelativeUrl).parseRelativeUrl(destination);\n                            parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                            const { url: newUrl , as: newAs  } = prepareUrlAs(_this, destination, destination);\n                            return _this.change(method, newUrl, newAs, options);\n                        }\n                        handleHardNavigation({\n                            url: destination,\n                            router: _this\n                        });\n                        return new Promise(()=>{});\n                    }\n                    nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                    // handle SSG data 404\n                    if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                        let notFoundRoute;\n                        try {\n                            yield _this.fetchComponent(\"/404\");\n                            notFoundRoute = \"/404\";\n                        } catch (_) {\n                            notFoundRoute = \"/_error\";\n                        }\n                        routeInfo = yield _this.getRouteInfo({\n                            route: notFoundRoute,\n                            pathname: notFoundRoute,\n                            query,\n                            as,\n                            resolvedAs,\n                            routeProps: {\n                                shallow: false\n                            },\n                            locale: nextState.locale,\n                            isPreview: nextState.isPreview,\n                            isNotFound: true\n                        });\n                        if (\"type\" in routeInfo) {\n                            throw new Error(\"Unexpected middleware effect on /404\");\n                        }\n                    }\n                }\n                if (isQueryUpdating && _this.pathname === \"/_error\" && ((ref4 = self.__NEXT_DATA__.props) == null ? void 0 : (ref5 = ref4.pageProps) == null ? void 0 : ref5.statusCode) === 500 && ((ref6 = routeInfo.props) == null ? void 0 : ref6.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                var _route;\n                // shallow routing is only allowed for same page URL changes.\n                const isValidShallowRoute = options.shallow && nextState.route === ((_route = routeInfo.route) != null ? _route : route);\n                var _scroll;\n                const shouldScroll = (_scroll = options.scroll) != null ? _scroll : !isQueryUpdating && !isValidShallowRoute;\n                const resetScroll = shouldScroll ? {\n                    x: 0,\n                    y: 0\n                } : null;\n                const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n                // the new state that the router gonna set\n                const upcomingRouterState = _extends({}, nextState, {\n                    route,\n                    pathname,\n                    query,\n                    asPath: cleanedAs,\n                    isFallback: false\n                });\n                // When the page being rendered is the 404 page, we should only update the\n                // query parameters. Route changes here might add the basePath when it\n                // wasn't originally present. This is also why this block is before the\n                // below `changeState` call which updates the browser's history (changing\n                // the URL).\n                if (isQueryUpdating && isErrorRoute) {\n                    var ref7, ref8, ref9;\n                    routeInfo = yield _this.getRouteInfo({\n                        route: _this.pathname,\n                        pathname: _this.pathname,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isQueryUpdating: isQueryUpdating && !_this.isFallback\n                    });\n                    if (\"type\" in routeInfo) {\n                        throw new Error(\"Unexpected middleware effect on \".concat(_this.pathname));\n                    }\n                    if (_this.pathname === \"/_error\" && ((ref7 = self.__NEXT_DATA__.props) == null ? void 0 : (ref8 = ref7.pageProps) == null ? void 0 : ref8.statusCode) === 500 && ((ref9 = routeInfo.props) == null ? void 0 : ref9.pageProps)) {\n                        // ensure statusCode is still correct for static 500 page\n                        // when updating query information\n                        routeInfo.props.pageProps.statusCode = 500;\n                    }\n                    try {\n                        yield _this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                    } catch (err) {\n                        if ((0, _isError).default(err) && err.cancelled) {\n                            Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                        }\n                        throw err;\n                    }\n                    return true;\n                }\n                Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n                _this.changeState(method, url, as, options);\n                // for query updates we can skip it if the state is unchanged and we don't\n                // need to scroll\n                // https://github.com/vercel/next.js/issues/37139\n                const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _compareStates).compareRouterStates(upcomingRouterState, _this.state);\n                if (!canSkipUpdating) {\n                    try {\n                        yield _this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                    } catch (e) {\n                        if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                        else throw e;\n                    }\n                    if (routeInfo.error) {\n                        if (!isQueryUpdating) {\n                            Router.events.emit(\"routeChangeError\", routeInfo.error, cleanedAs, routeProps);\n                        }\n                        throw routeInfo.error;\n                    }\n                    if (false) {}\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                    }\n                    // A hash mark # is the optional last part of a URL\n                    const hashRegex = /#.+$/;\n                    if (shouldScroll && hashRegex.test(as)) {\n                        _this.scrollToHash(as);\n                    }\n                }\n                return true;\n            } catch (err1) {\n                if ((0, _isError).default(err1) && err1.cancelled) {\n                    return false;\n                }\n                throw err1;\n            }\n        })();\n    }\n    changeState(method, url, as) {\n        let options = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(\"Warning: window.history.\".concat(method, \" is not available\"));\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils).getURL() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/en-US/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            console.error(err);\n            if (err.cancelled) {\n                // bubble up cancellation errors\n                throw err;\n            }\n            if ((0, _routeLoader).isAssetError(err) || loadErrorFail) {\n                Router.events.emit(\"routeChangeError\", err, as, routeProps);\n                // If we can't load the page it could be one of following reasons\n                //  1. Page doesn't exists\n                //  2. Page does exist in a different zone\n                //  3. Internal error while loading the page\n                // So, doing a hard reload is the proper way to deal with this.\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                // Changing the URL doesn't block executing the current code path.\n                // So let's throw a cancellation error stop the routing logic.\n                throw buildCancellationError();\n            }\n            try {\n                let props;\n                const { page: Component , styleSheets  } = yield _this.fetchComponent(\"/_error\");\n                const routeInfo = {\n                    props,\n                    Component,\n                    styleSheets,\n                    err,\n                    error: err\n                };\n                if (!routeInfo.props) {\n                    try {\n                        routeInfo.props = yield _this.getInitialProps(Component, {\n                            err,\n                            pathname,\n                            query\n                        });\n                    } catch (gipErr) {\n                        console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                        routeInfo.props = {};\n                    }\n                }\n                return routeInfo;\n            } catch (routeInfoErr) {\n                return _this.handleRouteInfoError((0, _isError).default(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n            }\n        })();\n    }\n    getRouteInfo(param) {\n        let { route: requestedRoute , pathname , query , as , resolvedAs , routeProps , locale , hasMiddleware , isPreview , unstable_skipClientCache , isQueryUpdating , isMiddlewareRewrite , isNotFound  } = param;\n        var _this = this;\n        return _async_to_generator(function*() {\n            /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n            try {\n                var ref, ref10, ref11, ref12;\n                const handleCancelled = getCancelledHandler({\n                    route,\n                    router: _this\n                });\n                let existingInfo = _this.components[route];\n                if (routeProps.shallow && existingInfo && _this.route === route) {\n                    return existingInfo;\n                }\n                if (hasMiddleware) {\n                    existingInfo = undefined;\n                }\n                let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n                const isBackground = isQueryUpdating;\n                const fetchNextDataParams = {\n                    dataHref: _this.pageLoader.getDataHref({\n                        href: (0, _formatUrl).formatWithValidation({\n                            pathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: isNotFound ? \"/404\" : resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: _this.isSsr,\n                    parseJSON: true,\n                    inflightCache: isBackground ? _this.sbc : _this.sdc,\n                    persistCache: !isPreview,\n                    isPrefetch: false,\n                    unstable_skipClientCache,\n                    isBackground\n                };\n                let data = isQueryUpdating && !isMiddlewareRewrite ? null : yield withMiddlewareEffects({\n                    fetchData: ()=>fetchNextData(fetchNextDataParams),\n                    asPath: isNotFound ? \"/404\" : resolvedAs,\n                    locale: locale,\n                    router: _this\n                }).catch((err)=>{\n                    // we don't hard error during query updating\n                    // as it's un-necessary and doesn't need to be fatal\n                    // unless it is a fallback route and the props can't\n                    // be loaded\n                    if (isQueryUpdating) {\n                        return null;\n                    }\n                    throw err;\n                });\n                // when rendering error routes we don't apply middleware\n                // effects\n                if (data && (pathname === \"/_error\" || pathname === \"/404\")) {\n                    data.effect = undefined;\n                }\n                if (isQueryUpdating) {\n                    if (!data) {\n                        data = {\n                            json: self.__NEXT_DATA__.props\n                        };\n                    } else {\n                        data.json = self.__NEXT_DATA__.props;\n                    }\n                }\n                handleCancelled();\n                if ((data == null ? void 0 : (ref = data.effect) == null ? void 0 : ref.type) === \"redirect-internal\" || (data == null ? void 0 : (ref10 = data.effect) == null ? void 0 : ref10.type) === \"redirect-external\") {\n                    return data.effect;\n                }\n                if ((data == null ? void 0 : (ref11 = data.effect) == null ? void 0 : ref11.type) === \"rewrite\") {\n                    const resolvedRoute = (0, _removeTrailingSlash).removeTrailingSlash(data.effect.resolvedHref);\n                    const pages = yield _this.pageLoader.getPageList();\n                    // during query updating the page must match although during\n                    // client-transition a redirect that doesn't match a page\n                    // can be returned and this should trigger a hard navigation\n                    // which is valid for incremental migration\n                    if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                        route = resolvedRoute;\n                        pathname = data.effect.resolvedHref;\n                        query = _extends({}, query, data.effect.parsedAs.query);\n                        resolvedAs = (0, _removeBasePath).removeBasePath((0, _normalizeLocalePath).normalizeLocalePath(data.effect.parsedAs.pathname, _this.locales).pathname);\n                        // Check again the cache with the new destination.\n                        existingInfo = _this.components[route];\n                        if (routeProps.shallow && existingInfo && _this.route === route && !hasMiddleware) {\n                            // If we have a match with the current route due to rewrite,\n                            // we can copy the existing information to the rewritten one.\n                            // Then, we return the information along with the matched route.\n                            return _extends({}, existingInfo, {\n                                route\n                            });\n                        }\n                    }\n                }\n                if ((0, _isApiRoute).isAPIRoute(route)) {\n                    handleHardNavigation({\n                        url: as,\n                        router: _this\n                    });\n                    return new Promise(()=>{});\n                }\n                const routeInfo = cachedRouteInfo || (yield _this.fetchComponent(route).then((res)=>({\n                        Component: res.page,\n                        styleSheets: res.styleSheets,\n                        __N_SSG: res.mod.__N_SSG,\n                        __N_SSP: res.mod.__N_SSP\n                    })));\n                if (true) {\n                    const { isValidElementType  } = __webpack_require__(/*! next/dist/compiled/react-is */ \"../node_modules/next/dist/compiled/react-is/index.js\");\n                    if (!isValidElementType(routeInfo.Component)) {\n                        throw new Error('The default export is not a React Component in page: \"'.concat(pathname, '\"'));\n                    }\n                }\n                const wasBailedPrefetch = data == null ? void 0 : (ref12 = data.response) == null ? void 0 : ref12.headers.get(\"x-middleware-skip\");\n                const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n                // For non-SSG prefetches that bailed before sending data\n                // we clear the cache to fetch full response\n                if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                    delete _this.sdc[data.dataHref];\n                }\n                const { props , cacheKey  } = yield _this._getData(_async_to_generator(function*() {\n                    if (shouldFetchData) {\n                        if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                            return {\n                                cacheKey: data.cacheKey,\n                                props: data.json\n                            };\n                        }\n                        const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : _this.pageLoader.getDataHref({\n                            href: (0, _formatUrl).formatWithValidation({\n                                pathname,\n                                query\n                            }),\n                            asPath: resolvedAs,\n                            locale\n                        });\n                        const fetched = yield fetchNextData({\n                            dataHref,\n                            isServerRender: _this.isSsr,\n                            parseJSON: true,\n                            inflightCache: wasBailedPrefetch ? {} : _this.sdc,\n                            persistCache: !isPreview,\n                            isPrefetch: false,\n                            unstable_skipClientCache\n                        });\n                        return {\n                            cacheKey: fetched.cacheKey,\n                            props: fetched.json || {}\n                        };\n                    }\n                    return {\n                        headers: {},\n                        props: yield _this.getInitialProps(routeInfo.Component, {\n                            pathname,\n                            query,\n                            asPath: as,\n                            locale,\n                            locales: _this.locales,\n                            defaultLocale: _this.defaultLocale\n                        })\n                    };\n                }));\n                // Only bust the data cache for SSP routes although\n                // middleware can skip cache per request with\n                // x-middleware-cache: no-cache as well\n                if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                    delete _this.sdc[cacheKey];\n                }\n                // we kick off a HEAD request in the background\n                // when a non-prefetch request is made to signal revalidation\n                if (!_this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n                props.pageProps = Object.assign({}, props.pageProps);\n                routeInfo.props = props;\n                routeInfo.route = route;\n                routeInfo.query = query;\n                routeInfo.resolvedAs = resolvedAs;\n                _this.components[route] = routeInfo;\n                return routeInfo;\n            } catch (err) {\n                return _this.handleRouteInfoError((0, _isError).getProperError(err), pathname, query, as, routeProps);\n            }\n        })();\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\");\n        const [newUrlNoHash, newHash] = as.split(\"#\");\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\");\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === \"\" || hash === \"top\") {\n            (0, _handleSmoothScroll).handleSmoothScroll(()=>window.scrollTo(0, 0));\n            return;\n        }\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash);\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash);\n        if (idEl) {\n            (0, _handleSmoothScroll).handleSmoothScroll(()=>idEl.scrollIntoView());\n            return;\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0];\n        if (nameEl) {\n            (0, _handleSmoothScroll).handleSmoothScroll(()=>nameEl.scrollIntoView());\n        }\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ prefetch(url) {\n        let asPath = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : url, options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        var _this = this;\n        return _async_to_generator(function*() {\n            // Prefetch is not supported in development mode because it would trigger on-demand-entries\n            if (true) {\n                return;\n            }\n            if ( true && (0, _isBot).isBot(window.navigator.userAgent)) {\n                // No prefetches for bots that render the link since they are typically navigating\n                // links via the equivalent of a hard navigation and hence never utilize these\n                // prefetches.\n                return;\n            }\n            let parsed = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            let { pathname , query  } = parsed;\n            const originalPathname = pathname;\n            if (false) {}\n            const pages = yield _this.pageLoader.getPageList();\n            let resolvedAs = asPath;\n            const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : _this.locale;\n            const isMiddlewareMatch = yield matchesMiddleware({\n                asPath: asPath,\n                locale: locale,\n                router: _this\n            });\n            if (false) {}\n            parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n            if ((0, _isDynamic).isDynamicRoute(parsed.pathname)) {\n                pathname = parsed.pathname;\n                parsed.pathname = pathname;\n                Object.assign(query, (0, _routeMatcher).getRouteMatcher((0, _routeRegex).getRouteRegex(parsed.pathname))((0, _parsePath).parsePath(asPath).pathname) || {});\n                if (!isMiddlewareMatch) {\n                    url = (0, _formatUrl).formatWithValidation(parsed);\n                }\n            }\n            const data =  false ? 0 : yield withMiddlewareEffects({\n                fetchData: ()=>fetchNextData({\n                        dataHref: _this.pageLoader.getDataHref({\n                            href: (0, _formatUrl).formatWithValidation({\n                                pathname: originalPathname,\n                                query\n                            }),\n                            skipInterpolation: true,\n                            asPath: resolvedAs,\n                            locale\n                        }),\n                        hasMiddleware: true,\n                        isServerRender: _this.isSsr,\n                        parseJSON: true,\n                        inflightCache: _this.sdc,\n                        persistCache: !_this.isPreview,\n                        isPrefetch: true\n                    }),\n                asPath: asPath,\n                locale: locale,\n                router: _this\n            });\n            /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === \"rewrite\") {\n                parsed.pathname = data.effect.resolvedHref;\n                pathname = data.effect.resolvedHref;\n                query = _extends({}, query, data.effect.parsedAs.query);\n                resolvedAs = data.effect.parsedAs.pathname;\n                url = (0, _formatUrl).formatWithValidation(parsed);\n            }\n            /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === \"redirect-external\") {\n                return;\n            }\n            const route = (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n            yield Promise.all([\n                _this.pageLoader._isSsg(route).then((isSsg)=>{\n                    return isSsg ? fetchNextData({\n                        dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : _this.pageLoader.getDataHref({\n                            href: url,\n                            asPath: resolvedAs,\n                            locale: locale\n                        }),\n                        isServerRender: false,\n                        parseJSON: true,\n                        inflightCache: _this.sdc,\n                        persistCache: !_this.isPreview,\n                        isPrefetch: true,\n                        unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                    }).then(()=>false) : false;\n                }),\n                _this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n            ]);\n        })();\n    }\n    fetchComponent(route) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: _this\n            });\n            try {\n                const componentResult = yield _this.pageLoader.loadPage(route);\n                handleCancelled();\n                return componentResult;\n            } catch (err) {\n                handleCancelled();\n                throw err;\n            }\n        })();\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then((param)=>{\n            let { text  } = param;\n            return {\n                data: text\n            };\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App  } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils).loadGetInitialProps(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname1, query1, as1, { initialProps , pageLoader , App , wrapApp , Component , err , subscription , isFallback , locale , locales , defaultLocale , domainLocales , isPreview  }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent  } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname , query  } = this;\n                this.changeState(\"replaceState\", (0, _formatUrl).formatWithValidation({\n                    pathname: (0, _addBasePath).addBasePath(pathname),\n                    query\n                }), (0, _utils).getURL());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url , as , options , key  } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname  } = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addBasePath).addBasePath(this.asPath) && pathname === (0, _addBasePath).addBasePath(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removeTrailingSlash).removeTrailingSlash(pathname1);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname1 !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        if (true) {\n            const { BloomFilter  } = __webpack_require__(/*! ../../lib/bloom-filter */ \"../node_modules/next/dist/shared/lib/bloom-filter/index.js\");\n            const staticFilterData = {\"bitset\":{\"size\":16,\"content\":\"0gI=\"},\"hashes\":7,\"size\":10};\n            const dynamicFilterData = {\"bitset\":{\"size\":16,\"content\":\"hgE=\"},\"hashes\":7,\"size\":10};\n            if (staticFilterData == null ? void 0 : staticFilterData.hashes) {\n                this._bfl_s = new BloomFilter(staticFilterData.size, staticFilterData.hashes);\n                this._bfl_s.import(staticFilterData);\n            }\n            if (dynamicFilterData == null ? void 0 : dynamicFilterData.hashes) {\n                this._bfl_d = new BloomFilter(dynamicFilterData.size, dynamicFilterData.hashes);\n                this._bfl_d.import(dynamicFilterData);\n            }\n        }\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isDynamic).isDynamicRoute(pathname1) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname: pathname1,\n            query: query1,\n            asPath: autoExportDynamic ? pathname1 : as1,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as1.startsWith(\"//\")) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils).getURL();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    options._shouldResolveHref = as1 !== pathname1;\n                    this.changeState(\"replaceState\", matches ? asPath : (0, _formatUrl).formatWithValidation({\n                        pathname: (0, _addBasePath).addBasePath(pathname1),\n                        query: query1\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener(\"popstate\", this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt).default();\nexports[\"default\"] = Router; //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});