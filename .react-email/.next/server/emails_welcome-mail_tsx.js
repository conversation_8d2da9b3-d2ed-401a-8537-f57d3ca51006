"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: logoContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                    src: \"https://dev-assets.store.flinkk.io/powderbyrne/logo.png\",\n                                    width: \"100\",\n                                    alt: \"PowderByrne\",\n                                    style: logo\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: buttonContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    href: resetLink,\n                                    style: button,\n                                    children: \"Access PB40 Ops\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Need assistance? Contact our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f9fafb\",\n    fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n    color: \"#1f2937\",\n    margin: \"0\",\n    padding: \"0\",\n    lineHeight: \"1.6\"\n};\nconst container = {\n    maxWidth: \"650px\",\n    margin: \"40px auto\",\n    padding: \"32px\",\n    backgroundColor: \"#ffffff\",\n    border: \"1px solid #e5e7eb\",\n    borderRadius: \"12px\",\n    boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.05)\"\n};\nconst box = {\n    padding: \"0\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n};\nconst logo = {\n    maxWidth: \"100px\",\n    display: \"block\",\n    margin: \"0 auto\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#1f2937\",\n    marginBottom: \"20px\",\n    fontSize: \"24px\",\n    fontWeight: \"bold\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#4b5563\",\n    fontSize: \"16px\",\n    lineHeight: \"24px\",\n    margin: \"16px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#1f2937\",\n    marginBottom: \"15px\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"bold\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f1f5f9, #e2e8f0)\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"16px\",\n    borderRadius: \"6px\",\n    marginTop: \"20px\",\n    textAlign: \"left\"\n};\nconst credentialItem = {\n    margin: \"8px 0\",\n    color: \"#1f2937\",\n    fontSize: \"16px\",\n    textAlign: \"left\"\n};\nconst passwordCode = {\n    backgroundColor: \"#1e293b\",\n    color: \"#ffffff\",\n    padding: \"8px 16px\",\n    borderRadius: \"6px\",\n    fontFamily: \"monospace\",\n    fontWeight: \"bold\",\n    fontSize: \"16px\",\n    display: \"inline-block\",\n    marginLeft: \"10px\",\n    letterSpacing: \"1px\",\n    border: \"1px solid #374151\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f1f5f9\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"15px\",\n    borderRadius: \"6px\",\n    margin: \"20px 0\",\n    textAlign: \"left\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#1f2937\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst buttonContainer = {\n    textAlign: \"center\",\n    margin: \"30px 0\",\n    padding: \"0 20px\"\n};\nconst button = {\n    display: \"inline-block\",\n    padding: \"16px 40px\",\n    backgroundColor: \"#2563eb\",\n    color: \"#ffffff\",\n    textDecoration: \"none\",\n    fontWeight: \"600\",\n    borderRadius: \"8px\",\n    fontSize: \"18px\",\n    textAlign: \"center\",\n    border: \"2px solid #2563eb\",\n    fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n    lineHeight: \"1.2\",\n    minWidth: \"200px\",\n    msoLineHeightRule: \"exactly\",\n    msoBorderAlt: \"none\",\n    msoHide: \"none\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fff7e6\",\n    border: \"1px solid #ffedc2\",\n    color: \"#8a6d3b\",\n    padding: \"15px\",\n    margin: \"20px 0\",\n    borderRadius: \"6px\",\n    textAlign: \"left\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#8a6d3b\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst welcomeMessage = {\n    marginTop: \"30px\",\n    textAlign: \"center\",\n    fontWeight: \"500\",\n    color: \"#1f2937\",\n    fontSize: \"16px\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"1px solid #f3f4f6\",\n    margin: \"40px 0\"\n};\nconst anchor = {\n    color: \"#2563eb\",\n    textDecoration: \"none\"\n};\nconst footer = {\n    fontSize: \"13px\",\n    lineHeight: \"1.4\",\n    color: \"#6b7280\",\n    textAlign: \"center\",\n    marginTop: \"30px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;