"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_plaid-verify-identity_tsx";
exports.ids = ["emails_plaid-verify-identity_tsx"];
exports.modules = {

/***/ "./emails/plaid-verify-identity.tsx":
/*!******************************************!*\
  !*** ./emails/plaid-verify-identity.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_plaid_verify_identity_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/plaid-verify-identity.tsx */ \"../emails/plaid-verify-identity.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_plaid_verify_identity_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvcGxhaWQtdmVyaWZ5LWlkZW50aXR5LnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwRDtBQUMxRCxpRUFBZUEseUVBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvcGxhaWQtdmVyaWZ5LWlkZW50aXR5LnRzeD8zYjRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy9wbGFpZC12ZXJpZnktaWRlbnRpdHkudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/plaid-verify-identity.tsx\n");

/***/ }),

/***/ "../emails/plaid-verify-identity.tsx":
/*!*******************************************!*\
  !*** ../emails/plaid-verify-identity.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PlaidVerifyIdentityEmail\": () => (/* binding */ PlaidVerifyIdentityEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : \"\";\nconst PlaidVerifyIdentityEmail = ({ validationCode =\"144833\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                lineNumber: 26,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                        style: container,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                src: `${baseUrl}/static/plaid-logo.png`,\n                                width: \"212\",\n                                height: \"88\",\n                                alt: \"Plaid\",\n                                style: logo\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: tertiary,\n                                children: \"Verify Your Identity\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                                style: secondary,\n                                children: \"Enter the following code to finish linking Venmo.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                                style: codeContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: code,\n                                    children: validationCode\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"Not expecting this email?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Contact\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: \"mailto:<EMAIL>\",\n                                        style: link,\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \" \",\n                                    \"if you did not request this code.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                        style: footer,\n                        children: \"Securely powered by Plaid.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n                lineNumber: 27,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/plaid-verify-identity.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlaidVerifyIdentityEmail);\nconst main = {\n    backgroundColor: \"#ffffff\",\n    fontFamily: \"HelveticaNeue,Helvetica,Arial,sans-serif\"\n};\nconst container = {\n    backgroundColor: \"#ffffff\",\n    border: \"1px solid #eee\",\n    borderRadius: \"5px\",\n    boxShadow: \"0 5px 10px rgba(20,50,70,.2)\",\n    marginTop: \"20px\",\n    width: \"360px\",\n    margin: \"0 auto\",\n    padding: \"68px 0 130px\"\n};\nconst logo = {\n    margin: \"0 auto\"\n};\nconst tertiary = {\n    color: \"#0a85ea\",\n    fontSize: \"11px\",\n    fontWeight: 700,\n    fontFamily: \"HelveticaNeue,Helvetica,Arial,sans-serif\",\n    height: \"16px\",\n    letterSpacing: \"0\",\n    lineHeight: \"16px\",\n    margin: \"16px 8px 8px 8px\",\n    textTransform: \"uppercase\",\n    textAlign: \"center\"\n};\nconst secondary = {\n    color: \"#000\",\n    display: \"inline-block\",\n    fontFamily: \"HelveticaNeue-Medium,Helvetica,Arial,sans-serif\",\n    fontSize: \"20px\",\n    fontWeight: 500,\n    lineHeight: \"24px\",\n    marginBottom: \"0\",\n    marginTop: \"0\",\n    textAlign: \"center\"\n};\nconst codeContainer = {\n    background: \"rgba(0,0,0,.05)\",\n    borderRadius: \"4px\",\n    margin: \"16px auto 14px\",\n    verticalAlign: \"middle\",\n    width: \"280px\"\n};\nconst code = {\n    color: \"#000\",\n    display: \"inline-block\",\n    fontFamily: \"HelveticaNeue-Bold\",\n    fontSize: \"32px\",\n    fontWeight: 700,\n    letterSpacing: \"6px\",\n    lineHeight: \"40px\",\n    paddingBottom: \"8px\",\n    paddingTop: \"8px\",\n    margin: \"0 auto\",\n    width: \"100%\",\n    textAlign: \"center\"\n};\nconst paragraph = {\n    color: \"#444\",\n    fontSize: \"15px\",\n    fontFamily: \"HelveticaNeue,Helvetica,Arial,sans-serif\",\n    letterSpacing: \"0\",\n    lineHeight: \"23px\",\n    padding: \"0 40px\",\n    margin: \"0\",\n    textAlign: \"center\"\n};\nconst link = {\n    color: \"#444\",\n    textDecoration: \"underline\"\n};\nconst footer = {\n    color: \"#000\",\n    fontSize: \"12px\",\n    fontWeight: 800,\n    letterSpacing: \"0\",\n    lineHeight: \"23px\",\n    margin: \"0\",\n    marginTop: \"20px\",\n    fontFamily: \"HelveticaNeue,Helvetica,Arial,sans-serif\",\n    textAlign: \"center\",\n    textTransform: \"uppercase\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///../emails/plaid-verify-identity.tsx\n");

/***/ })

};
;