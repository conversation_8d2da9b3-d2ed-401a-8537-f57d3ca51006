{"name": "tailwindcss", "version": "3.2.4", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "main": "lib/index.js", "types": "types/index.d.ts", "repository": "https://github.com/tailwindlabs/tailwindcss.git", "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "scripts": {"preswcify": "npm run generate && rimraf lib", "swcify": "swc src --out-dir lib --copy-files", "postswcify": "esbuild lib/cli-peer-dependencies.js --bundle --platform=node --outfile=peers/index.js", "rebuild-fixtures": "npm run swcify && node -r @swc/register scripts/rebuildFixtures.js", "prepublishOnly": "npm install --force && npm run swcify", "style": "eslint .", "pretest": "npm run generate", "test": "jest", "test:integrations": "npm run test --prefix ./integrations", "install:integrations": "node scripts/install-integrations.js", "generate:plugin-list": "node -r @swc/register scripts/create-plugin-list.js", "generate:types": "node -r @swc/register scripts/generate-types.js", "generate": "npm run generate:plugin-list && npm run generate:types", "release-channel": "node ./scripts/release-channel.js", "release-notes": "node ./scripts/release-notes.js"}, "files": ["src/*", "cli/*", "lib/*", "peers/*", "scripts/*.js", "stubs/*.stub.js", "nesting/*", "types/**/*", "*.d.ts", "*.css", "*.js"], "devDependencies": {"@swc/cli": "^0.1.57", "@swc/core": "^1.3.11", "@swc/jest": "^0.2.23", "@swc/register": "^0.1.10", "autoprefixer": "^10.4.13", "cssnano": "^5.1.14", "esbuild": "^0.15.12", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^28.1.3", "jest-diff": "^28.1.3", "prettier": "^2.7.1", "rimraf": "^3.0.0", "source-map-js": "^1.0.2"}, "peerDependencies": {"postcss": "^8.0.9"}, "dependencies": {"arg": "^5.0.2", "chokidar": "^3.5.3", "color-name": "^1.1.4", "detective": "^5.2.1", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.2.12", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "lilconfig": "^2.0.6", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.18", "postcss-import": "^14.1.0", "postcss-js": "^4.0.0", "postcss-load-config": "^3.1.4", "postcss-nested": "6.0.0", "postcss-selector-parser": "^6.0.10", "postcss-value-parser": "^4.2.0", "quick-lru": "^5.1.1", "resolve": "^1.22.1"}, "browserslist": ["> 1%", "not edge <= 18", "not ie 11", "not op_mini all"], "jest": {"testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/jest/customMatchers.js"], "testPathIgnorePatterns": ["/node_modules/", "/integrations/", "/standalone-cli/", "\\.test\\.skip\\.js$"], "transform": {"\\.js$": "@swc/jest"}}, "engines": {"node": ">=12.13.0"}}