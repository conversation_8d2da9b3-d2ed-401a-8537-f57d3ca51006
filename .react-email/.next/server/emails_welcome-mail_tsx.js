"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: bannerContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: bannerContent,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: logoContainer,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                                src: \"https://www.powderbyrne.com/wp-content/themes/monster-powder-byrne-theme/theme/images/stories-logo.png\",\n                                                width: \"120\",\n                                                alt: \"PowderByrne\",\n                                                style: logo\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            style: bannerText,\n                                            children: \"LUXURY FAMILY SKI HOLIDAYS\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            style: bannerSubtext,\n                                            children: \"WITH EXCLUSIVE SERVICES SINCE 1985\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: buttonContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    href: resetLink,\n                                    style: button,\n                                    children: \"Access PB40 Ops\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Need assistance? Contact our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f8fafc\",\n    fontFamily: \"Arial, 'Helvetica Neue', Helvetica, sans-serif\",\n    color: \"#1e293b\",\n    margin: \"0\",\n    padding: \"20px 10px\",\n    lineHeight: \"1.6\",\n    width: \"100%\"\n};\nconst container = {\n    maxWidth: \"580px\",\n    width: \"100%\",\n    margin: \"0 auto\",\n    padding: \"30px 20px\",\n    backgroundColor: \"#ffffff\",\n    borderRadius: \"16px\",\n    boxShadow: \"0 4px 6px -1px rgba(38, 93, 166, 0.1), 0 2px 4px -1px rgba(38, 93, 166, 0.06)\",\n    border: \"1px solid #e2e8f0\",\n    boxSizing: \"border-box\"\n};\nconst box = {\n    padding: \"0\",\n    width: \"100%\",\n    maxWidth: \"580px\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n};\nconst logo = {\n    maxWidth: \"100px\",\n    display: \"block\",\n    margin: \"0 auto\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#1e3a5f\",\n    marginBottom: \"32px\",\n    fontSize: \"32px\",\n    fontWeight: \"800\",\n    letterSpacing: \"-0.025em\",\n    lineHeight: \"1.2\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#475569\",\n    fontSize: \"18px\",\n    lineHeight: \"1.7\",\n    margin: \"24px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#0f172a\",\n    marginBottom: \"24px\",\n    fontSize: \"24px\",\n    textAlign: \"left\",\n    fontWeight: \"700\",\n    letterSpacing: \"-0.025em\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f8fafc, #f1f5f9)\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"32px\",\n    borderRadius: \"16px\",\n    marginTop: \"32px\",\n    textAlign: \"left\",\n    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n};\nconst credentialItem = {\n    margin: \"16px 0\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"500\"\n};\nconst passwordCode = {\n    backgroundColor: \"#0f172a\",\n    color: \"#ffffff\",\n    padding: \"12px 20px\",\n    borderRadius: \"8px\",\n    fontFamily: \"'Courier New', Courier, monospace\",\n    fontWeight: \"600\",\n    fontSize: \"16px\",\n    display: \"inline-block\",\n    marginLeft: \"12px\",\n    letterSpacing: \"0.5px\",\n    border: \"none\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f8fafc\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"24px\",\n    borderRadius: \"12px\",\n    margin: \"32px 0\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#0f172a\",\n    textAlign: \"left\",\n    fontSize: \"18px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst buttonContainer = {\n    textAlign: \"center\",\n    margin: \"30px 0\",\n    padding: \"0 10px\",\n    width: \"100%\"\n};\nconst button = {\n    display: \"inline-block\",\n    paddingTop: \"16px\",\n    paddingBottom: \"16px\",\n    paddingLeft: \"32px\",\n    paddingRight: \"32px\",\n    backgroundColor: \"#265DA6\",\n    color: \"#ffffff\",\n    textDecoration: \"none\",\n    fontWeight: \"700\",\n    borderRadius: \"12px\",\n    fontSize: \"16px\",\n    textAlign: \"center\",\n    border: \"none\",\n    fontFamily: \"Arial, 'Helvetica Neue', Helvetica, sans-serif\",\n    lineHeight: \"1.2\",\n    minWidth: \"200px\",\n    maxWidth: \"90%\",\n    boxShadow: \"0 4px 14px 0 rgba(38, 93, 166, 0.39)\",\n    letterSpacing: \"0.025em\",\n    msoLineHeightRule: \"exactly\",\n    msoBorderAlt: \"none\",\n    msoHide: \"none\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fef3c7\",\n    border: \"2px solid #fbbf24\",\n    color: \"#92400e\",\n    padding: \"24px\",\n    margin: \"32px 0\",\n    borderRadius: \"12px\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#92400e\",\n    textAlign: \"left\",\n    fontSize: \"16px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst welcomeMessage = {\n    marginTop: \"48px\",\n    textAlign: \"center\",\n    fontWeight: \"600\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    lineHeight: \"1.6\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"2px solid #f1f5f9\",\n    margin: \"48px 0\"\n};\nconst anchor = {\n    color: \"#265DA6\",\n    textDecoration: \"none\",\n    fontWeight: \"600\"\n};\nconst bannerContainer = {\n    width: \"100%\",\n    backgroundColor: \"#265DA6\",\n    borderRadius: \"12px\",\n    margin: \"20px 0\",\n    padding: \"0\",\n    overflow: \"hidden\",\n    backgroundImage: \"linear-gradient(135deg, #265DA6 0%, #1e4a8c 100%)\"\n};\nconst bannerContent = {\n    padding: \"24px 20px\",\n    textAlign: \"center\"\n};\nconst bannerText = {\n    color: \"#ffffff\",\n    fontSize: \"20px\",\n    fontWeight: \"700\",\n    letterSpacing: \"0.5px\",\n    margin: \"0\",\n    lineHeight: \"1.2\",\n    textTransform: \"uppercase\"\n};\nconst bannerSubtext = {\n    color: \"#e2e8f0\",\n    fontSize: \"14px\",\n    fontWeight: \"500\",\n    letterSpacing: \"0.3px\",\n    margin: \"8px 0 0 0\",\n    lineHeight: \"1.3\",\n    textTransform: \"uppercase\"\n};\nconst footer = {\n    fontSize: \"14px\",\n    lineHeight: \"1.6\",\n    color: \"#64748b\",\n    textAlign: \"center\",\n    marginTop: \"40px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;