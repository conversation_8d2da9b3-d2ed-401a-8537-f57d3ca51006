"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\n        :root {\n          --bg-color: #f9fafb;\n          --container-bg: #ffffff;\n          --text-primary: #1f2937;\n          --text-secondary: #4b5563;\n          --text-muted: #6b7280;\n          --border-color: #e5e7eb;\n          --border-light: #f3f4f6;\n          --shadow-color: rgba(0, 0, 0, 0.05);\n          --credentials-bg: #f1f5f9;\n          --warning-bg: #fff7e6;\n          --warning-border: #ffedc2;\n          --warning-text: #8a6d3b;\n          --primary-color: #2563eb;\n          --primary-hover: #1e40af;\n          --password-bg: #1e293b;\n          --password-text: #ffffff;\n        }\n\n        @media (prefers-color-scheme: dark) {\n          :root {\n            --bg-color: #111827;\n            --container-bg: #1f2937;\n            --text-primary: #f9fafb;\n            --text-secondary: #d1d5db;\n            --text-muted: #9ca3af;\n            --border-color: #374151;\n            --border-light: #4b5563;\n            --shadow-color: rgba(0, 0, 0, 0.3);\n            --credentials-bg: #2d3748;\n            --warning-bg: #4b3f28;\n            --warning-border: #6b5b2a;\n            --warning-text: #fcd34d;\n            --primary-color: #3b82f6;\n            --primary-hover: #2563eb;\n            --password-bg: #f9fafb;\n            --password-text: #1e293b;\n          }\n        }\n      `\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 76,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: logoContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                    src: \"https://dev-assets.store.flinkk.io/powderbyrne/logo.png\",\n                                    width: \"100\",\n                                    alt: \"Brand Logo\",\n                                    style: logo\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 44\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 44\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                href: resetLink,\n                                style: button,\n                                children: \"Access PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 41\n                                    }, undefined),\n                                    \"Need assistance? Contact our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 42\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 77,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f9fafb\",\n    fontFamily: \"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif\",\n    color: \"#1f2937\",\n    margin: \"0\",\n    padding: \"0\",\n    lineHeight: \"1.6\"\n};\nconst container = {\n    maxWidth: \"650px\",\n    margin: \"40px auto\",\n    padding: \"32px\",\n    backgroundColor: \"#ffffff\",\n    border: \"1px solid #e5e7eb\",\n    borderRadius: \"12px\",\n    boxShadow: \"0px 8px 20px rgba(0, 0, 0, 0.05)\"\n};\nconst box = {\n    padding: \"0\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n};\nconst logo = {\n    maxWidth: \"100px\",\n    display: \"block\",\n    margin: \"0 auto\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#1f2937\",\n    marginBottom: \"20px\",\n    fontSize: \"24px\",\n    fontWeight: \"bold\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#4b5563\",\n    fontSize: \"16px\",\n    lineHeight: \"24px\",\n    margin: \"16px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#1f2937\",\n    marginBottom: \"15px\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"bold\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f1f5f9, #e2e8f0)\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"16px\",\n    borderRadius: \"6px\",\n    marginTop: \"20px\",\n    textAlign: \"left\"\n};\nconst credentialItem = {\n    margin: \"8px 0\",\n    color: \"#1f2937\",\n    fontSize: \"16px\",\n    textAlign: \"left\"\n};\nconst passwordCode = {\n    backgroundColor: \"#1e293b\",\n    color: \"#ffffff\",\n    padding: \"6px 12px\",\n    borderRadius: \"6px\",\n    fontFamily: \"monospace\",\n    fontWeight: \"bold\",\n    fontSize: \"14px\",\n    display: \"inline-block\",\n    marginLeft: \"10px\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f1f5f9\",\n    border: \"1px solid #e5e7eb\",\n    padding: \"15px\",\n    borderRadius: \"6px\",\n    margin: \"20px 0\",\n    textAlign: \"left\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#1f2937\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst button = {\n    display: \"block\",\n    width: \"max-content\",\n    margin: \"20px auto\",\n    padding: \"12px 28px\",\n    backgroundColor: \"#2563eb\",\n    color: \"white\",\n    textDecoration: \"none\",\n    fontWeight: \"bold\",\n    borderRadius: \"8px\",\n    fontSize: \"16px\",\n    textAlign: \"center\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fff7e6\",\n    border: \"1px solid #ffedc2\",\n    color: \"#8a6d3b\",\n    padding: \"15px\",\n    margin: \"20px 0\",\n    borderRadius: \"6px\",\n    textAlign: \"left\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#8a6d3b\",\n    textAlign: \"left\",\n    fontSize: \"16px\"\n};\nconst welcomeMessage = {\n    marginTop: \"30px\",\n    textAlign: \"center\",\n    fontWeight: \"500\",\n    color: \"#1f2937\",\n    fontSize: \"16px\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"1px solid #f3f4f6\",\n    margin: \"40px 0\"\n};\nconst anchor = {\n    color: \"#2563eb\",\n    textDecoration: \"none\"\n};\nconst footer = {\n    fontSize: \"13px\",\n    lineHeight: \"1.4\",\n    color: \"#6b7280\",\n    textAlign: \"center\",\n    marginTop: \"30px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;