import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface PB40WelcomeEmailProps {
  user?: {
    first_name?: string;
    email?: string;
  };
  resetLink?: string;
  frontendURL?: string;
  tempPassword?: string;
}

export const PB40WelcomeEmail = ({
  user = { first_name: "<PERSON>", email: "<EMAIL>" },
  resetLink = "https://pb40ops.example.com/reset",
  frontendURL = "https://pb40ops.example.com",
  tempPassword = "temp123456",
}: PB40WelcomeEmailProps) => (
  <Html>
    <Preview>You've Been Invited to PB40 Ops</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={box}>
          {/* Banner Section */}
          <div style={bannerContainer}>
            <div style={bannerContent}>
              <div style={logoContainer}>
                <Img
                  src="https://www.powderbyrne.com/wp-content/themes/monster-powder-byrne-theme/theme/images/stories-logo.png"
                  width="120"
                  alt="PowderByrne"
                  style={logo}
                />
              </div>
              <Text style={bannerText}>LUXURY FAMILY SKI HOLIDAYS</Text>
              <Text style={bannerSubtext}>
                WITH EXCLUSIVE SERVICES SINCE 1985
              </Text>
            </div>
          </div>

          <Text style={heading}>You've Been Invited to PB40 Ops</Text>

          <Text style={paragraph}>
            Dear <strong>{user.first_name}</strong>,
          </Text>

          <Text style={paragraph}>
            You now have access to PB40 Ops — the system powering our inventory,
            pricing, supplier data, and concierge operations.
          </Text>

          <Text style={paragraph}>
            This platform is the core of how we deliver precision and client
            excellence. From managing room allocations to keeping supplier
            details accurate, every action inside PB40 Ops directly impacts our
            clients and business outcomes.
          </Text>

          <div style={accessDetailsSection}>
            <Text style={sectionHeading}>Your Access Details</Text>
            <div style={credentialsBox}>
              <Text style={credentialItem}>
                <strong>System:</strong> PB40 Ops
              </Text>
              <Text style={credentialItem}>
                <strong>Login URL:</strong>{" "}
                <Link href={resetLink} style={anchor}>
                  {frontendURL}
                </Link>
              </Text>
              <Text style={credentialItem}>
                <strong>Username:</strong> {user.email}
              </Text>
              <Text style={credentialItem}>
                <strong>Temporary Password:</strong>
                <span style={passwordCode}>{tempPassword}</span>
              </Text>
            </div>
          </div>

          <div style={firstStepBox}>
            <Text style={firstStepText}>
              <strong>First Step:</strong> Log in and reset your password under
              Settings → Profile → Change Password.
            </Text>
          </div>

          <div style={buttonContainer}>
            <Button href={resetLink} style={button}>
              Access PB40 Ops
            </Button>
          </div>

          <div style={securityNotice}>
            <Text style={securityText}>
              <strong>⚠️ Important:</strong> Do not share your credentials. All
              actions inside PB40 Ops are logged for security and
              accountability.
            </Text>
          </div>

          <Text style={welcomeMessage}>
            Welcome aboard — let's get started.
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            <strong>Team Flinkk</strong>
            <br />
            Need assistance? Contact our{" "}
            <Link href={`${frontendURL}/contact-us`} style={anchor}>
              support team
            </Link>
            .
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default PB40WelcomeEmail;

const main = {
  backgroundColor: "#f8fafc",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
  color: "#1e293b",
  margin: "0",
  padding: "20px 10px",
  lineHeight: "1.6",
  width: "100%",
};

const container = {
  maxWidth: "580px",
  width: "100%",
  margin: "0 auto",
  padding: "30px 20px",
  backgroundColor: "#ffffff",
  borderRadius: "16px",
  boxShadow:
    "0 4px 6px -1px rgba(38, 93, 166, 0.1), 0 2px 4px -1px rgba(38, 93, 166, 0.06)",
  border: "1px solid #e2e8f0",
  boxSizing: "border-box" as const,
};

const box = {
  padding: "0",
  width: "100%",
  maxWidth: "580px",
};

const logoContainer = {
  textAlign: "center" as const,
  marginBottom: "20px",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

const logo = {
  maxWidth: "100px",
  display: "block",
  margin: "0 auto",
};

const heading = {
  textAlign: "center" as const,
  color: "#1e3a5f",
  marginBottom: "32px",
  fontSize: "32px",
  fontWeight: "800",
  letterSpacing: "-0.025em",
  lineHeight: "1.2",
};

const paragraph = {
  textAlign: "center" as const,
  color: "#475569",
  fontSize: "18px",
  lineHeight: "1.7",
  margin: "24px 0",
};

const accessDetailsSection = {
  margin: "20px 0",
};

const sectionHeading = {
  color: "#0f172a",
  marginBottom: "24px",
  fontSize: "24px",
  textAlign: "left" as const,
  fontWeight: "700",
  letterSpacing: "-0.025em",
};

const credentialsBox = {
  background: "linear-gradient(135deg, #f8fafc, #f1f5f9)",
  border: "2px solid #e2e8f0",
  padding: "32px",
  borderRadius: "16px",
  marginTop: "32px",
  textAlign: "left" as const,
  boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
};

const credentialItem = {
  margin: "16px 0",
  color: "#0f172a",
  fontSize: "18px",
  textAlign: "left" as const,
  fontWeight: "500",
};

const passwordCode = {
  backgroundColor: "#0f172a",
  color: "#ffffff",
  padding: "12px 20px",
  borderRadius: "8px",
  fontFamily: "'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace",
  fontWeight: "600",
  fontSize: "16px",
  display: "inline-block",
  marginLeft: "12px",
  letterSpacing: "0.5px",
  border: "none",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
};

const firstStepBox = {
  backgroundColor: "#f8fafc",
  border: "2px solid #e2e8f0",
  padding: "24px",
  borderRadius: "12px",
  margin: "32px 0",
  textAlign: "left" as const,
  boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
};

const firstStepText = {
  margin: "0",
  color: "#0f172a",
  textAlign: "left" as const,
  fontSize: "18px",
  fontWeight: "500",
  lineHeight: "1.6",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "30px 0",
  padding: "0 10px",
  width: "100%",
};

const button = {
  display: "inline-block",
  paddingTop: "16px",
  paddingBottom: "16px",
  paddingLeft: "32px",
  paddingRight: "32px",
  backgroundColor: "#265DA6",
  color: "#ffffff",
  textDecoration: "none",
  fontWeight: "700",
  borderRadius: "12px",
  fontSize: "16px",
  textAlign: "center" as const,
  border: "none",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
  lineHeight: "1.2",
  minWidth: "200px",
  maxWidth: "90%",
  boxShadow: "0 4px 14px 0 rgba(38, 93, 166, 0.39)",
  letterSpacing: "0.025em",
  msoLineHeightRule: "exactly",
  msoBorderAlt: "none",
  msoHide: "none",
};

const securityNotice = {
  backgroundColor: "#fef3c7",
  border: "2px solid #fbbf24",
  color: "#92400e",
  padding: "24px",
  margin: "32px 0",
  borderRadius: "12px",
  textAlign: "left" as const,
  boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
};

const securityText = {
  margin: "0",
  color: "#92400e",
  textAlign: "left" as const,
  fontSize: "16px",
  fontWeight: "500",
  lineHeight: "1.6",
};

const welcomeMessage = {
  marginTop: "48px",
  textAlign: "center" as const,
  fontWeight: "600",
  color: "#0f172a",
  fontSize: "18px",
  lineHeight: "1.6",
};

const hr = {
  border: "none",
  borderTop: "2px solid #f1f5f9",
  margin: "48px 0",
};

const anchor = {
  color: "#265DA6",
  textDecoration: "none",
  fontWeight: "600",
};

const bannerContainer = {
  width: "100%",
  backgroundColor: "#265DA6",
  borderRadius: "12px",
  margin: "20px 0",
  padding: "0",
  overflow: "hidden",
  backgroundImage: "linear-gradient(135deg, #265DA6 0%, #1e4a8c 100%)",
};

const bannerContent = {
  padding: "24px 20px",
  textAlign: "center" as const,
};

const bannerText = {
  color: "#ffffff",
  fontSize: "20px",
  fontWeight: "700",
  letterSpacing: "0.5px",
  margin: "0",
  lineHeight: "1.2",
  textTransform: "uppercase" as const,
};

const bannerSubtext = {
  color: "#e2e8f0",
  fontSize: "14px",
  fontWeight: "500",
  letterSpacing: "0.3px",
  margin: "8px 0 0 0",
  lineHeight: "1.3",
  textTransform: "uppercase" as const,
};

const footer = {
  fontSize: "14px",
  lineHeight: "1.6",
  color: "#64748b",
  textAlign: "center" as const,
  marginTop: "40px",
};
