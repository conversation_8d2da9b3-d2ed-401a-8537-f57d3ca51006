"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"StripeWelcomeEmail\": () => (/* binding */ StripeWelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : \"\";\nconst StripeWelcomeEmail = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You're now ready to make live transactions with Stripe!\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                src: `${baseUrl}/static/stripe-logo.png`,\n                                width: \"49\",\n                                height: \"21\",\n                                alt: \"Stripe\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"Thanks for submitting your account information. You're now ready to make live transactions with Stripe!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You can view your payments and a variety of other information about your account right from your dashboard.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                pX: 10,\n                                pY: 10,\n                                style: button,\n                                href: \"https://dashboard.stripe.com/login\",\n                                children: \"View your Stripe Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"If you haven't finished your integration, you might find our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        style: anchor,\n                                        href: \"https://stripe.com/docs\",\n                                        children: \"docs\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"handy.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Once you're ready to start accepting payments, you'll just need to use your live\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        style: anchor,\n                                        href: \"https://dashboard.stripe.com/login?redirect=%2Fapikeys\",\n                                        children: \"API keys\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"instead of your test API keys. Your account can simultaneously be used for both test and live requests, so you can continue testing while accepting live payments. Check out our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        style: anchor,\n                                        href: \"https://stripe.com/docs/dashboard\",\n                                        children: \"tutorial about account basics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Finally, we've put together a\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        style: anchor,\n                                        href: \"https://stripe.com/docs/checklist/website\",\n                                        children: \"quick checklist\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"to ensure your website conforms to card network standards.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"We'll be here to help you with any step along the way. You can find answers to most questions and get in touch with us on our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        style: anchor,\n                                        href: \"https://support.stripe.com/\",\n                                        children: \"support site\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"— The Stripe team\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: \"Stripe, 354 Oyster Point Blvd, South San Francisco, CA 94080\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 24,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StripeWelcomeEmail);\nconst main = {\n    backgroundColor: \"#f6f9fc\",\n    fontFamily: '-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Ubuntu,sans-serif'\n};\nconst container = {\n    backgroundColor: \"#ffffff\",\n    margin: \"0 auto\",\n    padding: \"20px 0 48px\",\n    marginBottom: \"64px\"\n};\nconst box = {\n    padding: \"0 48px\"\n};\nconst hr = {\n    borderColor: \"#e6ebf1\",\n    margin: \"20px 0\"\n};\nconst paragraph = {\n    color: \"#525f7f\",\n    fontSize: \"16px\",\n    lineHeight: \"24px\",\n    textAlign: \"left\"\n};\nconst anchor = {\n    color: \"#556cd6\"\n};\nconst button = {\n    backgroundColor: \"#656ee8\",\n    borderRadius: \"5px\",\n    color: \"#fff\",\n    fontSize: \"16px\",\n    fontWeight: \"bold\",\n    textDecoration: \"none\",\n    textAlign: \"center\",\n    display: \"block\",\n    width: \"100%\"\n};\nconst footer = {\n    color: \"#8898aa\",\n    fontSize: \"12px\",\n    lineHeight: \"16px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;