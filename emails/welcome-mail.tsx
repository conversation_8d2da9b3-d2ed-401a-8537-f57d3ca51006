import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface PB40WelcomeEmailProps {
  user?: {
    first_name?: string;
    email?: string;
  };
  resetLink?: string;
  frontendURL?: string;
  tempPassword?: string;
}

export const PB40WelcomeEmail = ({
  user = { first_name: 'John', email: '<EMAIL>' },
  resetLink = 'https://pb40ops.example.com/reset',
  frontendURL = 'https://pb40ops.example.com',
  tempPassword = 'temp123456'
}: PB40WelcomeEmailProps) => (
  <Html>
    <Head>
      <style>{`
        :root {
          --bg-color: #f9fafb;
          --container-bg: #ffffff;
          --text-primary: #1f2937;
          --text-secondary: #4b5563;
          --text-muted: #6b7280;
          --border-color: #e5e7eb;
          --border-light: #f3f4f6;
          --shadow-color: rgba(0, 0, 0, 0.05);
          --credentials-bg: #f1f5f9;
          --warning-bg: #fff7e6;
          --warning-border: #ffedc2;
          --warning-text: #8a6d3b;
          --primary-color: #2563eb;
          --primary-hover: #1e40af;
          --password-bg: #1e293b;
          --password-text: #ffffff;
        }

        @media (prefers-color-scheme: dark) {
          :root {
            --bg-color: #111827;
            --container-bg: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --text-muted: #9ca3af;
            --border-color: #374151;
            --border-light: #4b5563;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --credentials-bg: #2d3748;
            --warning-bg: #4b3f28;
            --warning-border: #6b5b2a;
            --warning-text: #fcd34d;
            --primary-color: #3b82f6;
            --primary-hover: #2563eb;
            --password-bg: #f9fafb;
            --password-text: #1e293b;
          }
        }
      `}</style>
    </Head>
    <Preview>You've Been Invited to PB40 Ops</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={box}>
          <div style={logoContainer}>
            <Img
              src="https://dev-assets.store.flinkk.io/powderbyrne/logo.png"
              width="100"
              alt="Brand Logo"
              style={logo}
            />
          </div>

          <Text style={heading}>You've Been Invited to PB40 Ops</Text>

          <Text style={paragraph}>
            Dear <strong>{user.first_name}</strong>,
          </Text>

          <Text style={paragraph}>
            You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.
          </Text>

          <Text style={paragraph}>
            This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.
          </Text>

          <div style={accessDetailsSection}>
            <Text style={sectionHeading}>Your Access Details</Text>
            <div style={credentialsBox}>
              <Text style={credentialItem}><strong>System:</strong> PB40 Ops</Text>
              <Text style={credentialItem}>
                <strong>Login URL:</strong> <Link href={resetLink} style={anchor}>{frontendURL}</Link>
              </Text>
              <Text style={credentialItem}><strong>Username:</strong> {user.email}</Text>
              <div style={passwordContainer}>
                <Text style={credentialItem}>
                  <strong>Temporary Password:</strong>
                  <span style={passwordCode}>{tempPassword}</span>
                </Text>
                <Button
                  href={`javascript:navigator.clipboard.writeText('${tempPassword}')`}
                  style={copyButton}
                >
                  Copy
                </Button>
              </div>
            </div>
          </div>

          <div style={firstStepBox}>
            <Text style={firstStepText}>
              <strong>First Step:</strong> Log in and reset your password under Settings → Profile → Change Password.
            </Text>
          </div>

          <div style={buttonContainer}>
            <Button
              href={resetLink}
              style={button}
            >
              Access PB40 Ops
            </Button>
          </div>

          <div style={securityNotice}>
            <Text style={securityText}>
              <strong>⚠️ Important:</strong> Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.
            </Text>
          </div>

          <Text style={welcomeMessage}>
            Welcome aboard — let's get started.
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            <strong>Team Flinkk</strong><br />
            Need assistance? Contact our <Link href={`${frontendURL}/contact-us`} style={anchor}>support team</Link>.
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default PB40WelcomeEmail;

const main = {
  backgroundColor: '#f9fafb',
  fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
  color: '#1f2937',
  margin: '0',
  padding: '0',
  lineHeight: '1.6',
};

const container = {
  maxWidth: '650px',
  margin: '40px auto',
  padding: '32px',
  backgroundColor: '#ffffff',
  border: '1px solid #e5e7eb',
  borderRadius: '12px',
  boxShadow: '0px 8px 20px rgba(0, 0, 0, 0.05)',
};

const box = {
  padding: '0',
};

const logoContainer = {
  textAlign: 'center' as const,
  marginBottom: '20px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
};

const logo = {
  maxWidth: '100px',
  display: 'block',
  margin: '0 auto',
};

const heading = {
  textAlign: 'center' as const,
  color: '#1f2937',
  marginBottom: '20px',
  fontSize: '24px',
  fontWeight: 'bold',
};

const paragraph = {
  textAlign: 'center' as const,
  color: '#4b5563',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const accessDetailsSection = {
  margin: '20px 0',
};

const sectionHeading = {
  color: '#1f2937',
  marginBottom: '15px',
  fontSize: '18px',
  textAlign: 'left' as const,
  fontWeight: 'bold',
};

const credentialsBox = {
  background: 'linear-gradient(135deg, #f1f5f9, #e2e8f0)',
  border: '1px solid #e5e7eb',
  padding: '16px',
  borderRadius: '6px',
  marginTop: '20px',
  textAlign: 'left' as const,
};

const credentialItem = {
  margin: '8px 0',
  color: '#1f2937',
  fontSize: '16px',
  textAlign: 'left' as const,
};

const passwordContainer = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '10px',
  margin: '8px 0',
};

const passwordCode = {
  backgroundColor: '#1e293b',
  color: '#ffffff',
  padding: '6px 12px',
  borderRadius: '6px',
  fontFamily: 'monospace',
  fontWeight: 'bold',
  fontSize: '14px',
  display: 'inline-block',
  marginLeft: '10px',
};

const copyButton = {
  backgroundColor: '#6b7280',
  color: 'white',
  padding: '4px 8px',
  borderRadius: '4px',
  fontSize: '12px',
  fontWeight: 'bold',
  textDecoration: 'none',
  border: 'none',
  cursor: 'pointer',
  display: 'inline-block',
  minWidth: '50px',
  textAlign: 'center' as const,
};

const firstStepBox = {
  backgroundColor: '#f1f5f9',
  border: '1px solid #e5e7eb',
  padding: '15px',
  borderRadius: '6px',
  margin: '20px 0',
  textAlign: 'left' as const,
};

const firstStepText = {
  margin: '0',
  color: '#1f2937',
  textAlign: 'left' as const,
  fontSize: '16px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '30px 0',
};

const button = {
  display: 'inline-block',
  padding: '14px 32px',
  backgroundColor: '#2563eb',
  color: 'white !important',
  textDecoration: 'none',
  fontWeight: 'bold',
  borderRadius: '8px',
  fontSize: '16px',
  textAlign: 'center' as const,
  border: 'none',
  cursor: 'pointer',
  transition: 'background-color 0.3s ease',
  boxShadow: '0 2px 4px rgba(37, 99, 235, 0.2)',
};

const securityNotice = {
  backgroundColor: '#fff7e6',
  border: '1px solid #ffedc2',
  color: '#8a6d3b',
  padding: '15px',
  margin: '20px 0',
  borderRadius: '6px',
  textAlign: 'left' as const,
};

const securityText = {
  margin: '0',
  color: '#8a6d3b',
  textAlign: 'left' as const,
  fontSize: '16px',
};

const welcomeMessage = {
  marginTop: '30px',
  textAlign: 'center' as const,
  fontWeight: '500',
  color: '#1f2937',
  fontSize: '16px',
};

const hr = {
  border: 'none',
  borderTop: '1px solid #f3f4f6',
  margin: '40px 0',
};

const anchor = {
  color: '#2563eb',
  textDecoration: 'none',
};

const footer = {
  fontSize: '13px',
  lineHeight: '1.4',
  color: '#6b7280',
  textAlign: 'center' as const,
  marginTop: '30px',
};
