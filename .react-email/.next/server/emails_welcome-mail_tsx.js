"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "emails_welcome-mail_tsx";
exports.ids = ["emails_welcome-mail_tsx"];
exports.modules = {

/***/ "./emails/welcome-mail.tsx":
/*!*********************************!*\
  !*** ./emails/welcome-mail.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../emails/welcome-mail.tsx */ \"../emails/welcome-mail.tsx\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_emails_welcome_mail_tsx__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNqRCxpRUFBZUEsZ0VBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFjdC1lbWFpbC1jbGllbnQvLi9lbWFpbHMvd2VsY29tZS1tYWlsLnRzeD9iYjY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNYWlsIGZyb20gJy4uLy4uL2VtYWlscy93ZWxjb21lLW1haWwudHN4JztcbmV4cG9ydCBkZWZhdWx0IE1haWw7Il0sIm5hbWVzIjpbIk1haWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./emails/welcome-mail.tsx\n");

/***/ }),

/***/ "../emails/welcome-mail.tsx":
/*!**********************************!*\
  !*** ../emails/welcome-mail.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PB40WelcomeEmail\": () => (/* binding */ PB40WelcomeEmail),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-email/components */ \"@react-email/components\");\n/* harmony import */ var _react_email_components__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_react_email_components__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst PB40WelcomeEmail = ({ user ={\n    first_name: \"John\",\n    email: \"<EMAIL>\"\n} , resetLink =\"https://pb40ops.example.com/reset\" , frontendURL =\"https://pb40ops.example.com\" , tempPassword =\"temp123456\"  })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Preview, {\n                children: \"You've Been Invited to PB40 Ops\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Body, {\n                style: main,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                    style: container,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Section, {\n                        style: box,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: logoContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Img, {\n                                    src: \"https://dev-assets.store.flinkk.io/powderbyrne/logo.png\",\n                                    width: \"100\",\n                                    alt: \"PowderByrne\",\n                                    style: logo\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: heading,\n                                children: \"You've Been Invited to PB40 Ops\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: [\n                                    \"Dear \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: user.first_name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \",\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"You now have access to PB40 Ops — the system powering our inventory, pricing, supplier data, and concierge operations.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: paragraph,\n                                children: \"This platform is the core of how we deliver precision and client excellence. From managing room allocations to keeping supplier details accurate, every action inside PB40 Ops directly impacts our clients and business outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: accessDetailsSection,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        style: sectionHeading,\n                                        children: \"Your Access Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: credentialsBox,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"System:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" PB40 Ops\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Login URL:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                        href: resetLink,\n                                                        style: anchor,\n                                                        children: frontendURL\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Username:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \" \",\n                                                    user.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                style: credentialItem,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Temporary Password:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: passwordCode,\n                                                        children: tempPassword\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: firstStepBox,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: firstStepText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"First Step:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Log in and reset your password under Settings → Profile → Change Password.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: buttonContainer,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    href: resetLink,\n                                    style: button,\n                                    children: \"Access PB40 Ops\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: securityNotice,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    style: securityText,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"⚠️ Important:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" Do not share your credentials. All actions inside PB40 Ops are logged for security and accountability.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: welcomeMessage,\n                                children: \"Welcome aboard — let's get started.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Hr, {\n                                style: hr\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                style: footer,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Team Flinkk\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Need assistance? Contact our\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_email_components__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                        href: `${frontendURL}/contact-us`,\n                                        style: anchor,\n                                        children: \"support team\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/incresco/flinkk/react-email-starter/emails/welcome-mail.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PB40WelcomeEmail);\nconst main = {\n    backgroundColor: \"#f8fafc\",\n    fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif\",\n    color: \"#1e293b\",\n    margin: \"0\",\n    padding: \"20px 0\",\n    lineHeight: \"1.6\"\n};\nconst container = {\n    maxWidth: \"600px\",\n    margin: \"0 auto\",\n    padding: \"40px\",\n    backgroundColor: \"#ffffff\",\n    borderRadius: \"16px\",\n    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)\",\n    border: \"1px solid #f1f5f9\"\n};\nconst box = {\n    padding: \"0\"\n};\nconst logoContainer = {\n    textAlign: \"center\",\n    marginBottom: \"20px\",\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\"\n};\nconst logo = {\n    maxWidth: \"100px\",\n    display: \"block\",\n    margin: \"0 auto\"\n};\nconst heading = {\n    textAlign: \"center\",\n    color: \"#0f172a\",\n    marginBottom: \"32px\",\n    fontSize: \"32px\",\n    fontWeight: \"800\",\n    letterSpacing: \"-0.025em\",\n    lineHeight: \"1.2\"\n};\nconst paragraph = {\n    textAlign: \"center\",\n    color: \"#475569\",\n    fontSize: \"18px\",\n    lineHeight: \"1.7\",\n    margin: \"24px 0\"\n};\nconst accessDetailsSection = {\n    margin: \"20px 0\"\n};\nconst sectionHeading = {\n    color: \"#0f172a\",\n    marginBottom: \"24px\",\n    fontSize: \"24px\",\n    textAlign: \"left\",\n    fontWeight: \"700\",\n    letterSpacing: \"-0.025em\"\n};\nconst credentialsBox = {\n    background: \"linear-gradient(135deg, #f8fafc, #f1f5f9)\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"32px\",\n    borderRadius: \"16px\",\n    marginTop: \"32px\",\n    textAlign: \"left\",\n    boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n};\nconst credentialItem = {\n    margin: \"16px 0\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    textAlign: \"left\",\n    fontWeight: \"500\"\n};\nconst passwordCode = {\n    backgroundColor: \"#0f172a\",\n    color: \"#ffffff\",\n    padding: \"12px 20px\",\n    borderRadius: \"8px\",\n    fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace\",\n    fontWeight: \"600\",\n    fontSize: \"16px\",\n    display: \"inline-block\",\n    marginLeft: \"12px\",\n    letterSpacing: \"0.5px\",\n    border: \"none\",\n    boxShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepBox = {\n    backgroundColor: \"#f8fafc\",\n    border: \"2px solid #e2e8f0\",\n    padding: \"24px\",\n    borderRadius: \"12px\",\n    margin: \"32px 0\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst firstStepText = {\n    margin: \"0\",\n    color: \"#0f172a\",\n    textAlign: \"left\",\n    fontSize: \"18px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst buttonContainer = {\n    textAlign: \"center\",\n    margin: \"30px 0\",\n    padding: \"0 20px\"\n};\nconst button = {\n    display: \"inline-block\",\n    paddingTop: \"18px\",\n    paddingBottom: \"18px\",\n    paddingLeft: \"48px\",\n    paddingRight: \"48px\",\n    backgroundColor: \"#3b82f6\",\n    color: \"#ffffff\",\n    textDecoration: \"none\",\n    fontWeight: \"700\",\n    borderRadius: \"12px\",\n    fontSize: \"18px\",\n    textAlign: \"center\",\n    border: \"none\",\n    fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif\",\n    lineHeight: \"1.2\",\n    minWidth: \"240px\",\n    boxShadow: \"0 4px 14px 0 rgba(59, 130, 246, 0.39)\",\n    letterSpacing: \"0.025em\",\n    msoLineHeightRule: \"exactly\",\n    msoBorderAlt: \"none\",\n    msoHide: \"none\"\n};\nconst securityNotice = {\n    backgroundColor: \"#fef3c7\",\n    border: \"2px solid #fbbf24\",\n    color: \"#92400e\",\n    padding: \"24px\",\n    margin: \"32px 0\",\n    borderRadius: \"12px\",\n    textAlign: \"left\",\n    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\"\n};\nconst securityText = {\n    margin: \"0\",\n    color: \"#92400e\",\n    textAlign: \"left\",\n    fontSize: \"16px\",\n    fontWeight: \"500\",\n    lineHeight: \"1.6\"\n};\nconst welcomeMessage = {\n    marginTop: \"48px\",\n    textAlign: \"center\",\n    fontWeight: \"600\",\n    color: \"#0f172a\",\n    fontSize: \"18px\",\n    lineHeight: \"1.6\"\n};\nconst hr = {\n    border: \"none\",\n    borderTop: \"2px solid #f1f5f9\",\n    margin: \"48px 0\"\n};\nconst anchor = {\n    color: \"#3b82f6\",\n    textDecoration: \"none\",\n    fontWeight: \"600\"\n};\nconst footer = {\n    fontSize: \"14px\",\n    lineHeight: \"1.6\",\n    color: \"#64748b\",\n    textAlign: \"center\",\n    marginTop: \"40px\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../emails/welcome-mail.tsx\n");

/***/ })

};
;